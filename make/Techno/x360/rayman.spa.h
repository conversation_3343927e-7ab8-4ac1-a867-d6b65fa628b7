////////////////////////////////////////////////////////////////////
//
// C:\Ubisoft\Rayman\ITF\make\Techno\x360\rayman.spa.h
//
// Auto-generated on Wednesday, 12 October 2011 at 10:46:47
// Xbox LIVE Game Config project version 1.0.137.0
// SPA Compiler version 1.0.0.0
//
////////////////////////////////////////////////////////////////////

#ifndef __RAYMAN_ORIGINS_SPA_H__
#define __RAYMAN_ORIGINS_SPA_H__

#ifdef __cplusplus
extern "C" {
#endif

//
// Title info
//

#define TITLEID_RAYMAN_ORIGINS                      0x55530884

//
// Context ids
//
// These values are passed as the dwContextId to XUserSetContext.
//


//
// Context values
//
// These values are passed as the dwContextValue to XUserSetContext.
//

// Values for X_CONTEXT_PRESENCE

#define CONTEXT_PRESENCE_INTHEMENUS                 0
#define CONTEXT_PRESENCE_INWORLDMAP                 1
#define CONTEXT_PRESENCE_PLAYING_JUNGLE_A           2
#define CONTEXT_PRESENCE_PLAYING_JUNGLE_B           3
#define CONTEXT_PRESENCE_PLAYING_MUSIC_A            4
#define CONTEXT_PRESENCE_PLAYING_MUSIC_B            5
#define CONTEXT_PRESENCE_PLAYING_FOOD_A             6
#define CONTEXT_PRESENCE_PLAYING_FOOD_B             7
#define CONTEXT_PRESENCE_PLAYING_OCEAN_A            8
#define CONTEXT_PRESENCE_PLAYING_OCEAN_B            9
#define CONTEXT_PRESENCE_PLAYING_MOUNTAIN_A         10
#define CONTEXT_PRESENCE_PLAYING_MOUNTAIN_B         11
#define CONTEXT_PRESENCE_PLAYING_DEATH              12
#define CONTEXT_PRESENCE_PLAYING_HOME               13
#define CONTEXT_PRESENCE_NOTPLAYING                 14

// Values for X_CONTEXT_GAME_MODE

#define CONTEXT_GAME_MODE_MENU                      0

//
// Property ids
//
// These values are passed as the dwPropertyId value to XUserSetProperty
// and as the dwPropertyId value in the XUSER_PROPERTY structure.
//


//
// Achievement ids
//
// These values are used in the dwAchievementId member of the
// XUSER_ACHIEVEMENT structure that is used with
// XUserWriteAchievements and XUserCreateAchievementEnumerator.
//

#define ACHIEVEMENT_TRP01                           1
#define ACHIEVEMENT_TRP02                           2
#define ACHIEVEMENT_TRP03                           3
#define ACHIEVEMENT_TRP04                           4
#define ACHIEVEMENT_TRP05                           5
#define ACHIEVEMENT_TRP06                           6
#define ACHIEVEMENT_TRP07                           7
#define ACHIEVEMENT_TRP08                           8
#define ACHIEVEMENT_TRP09                           9
#define ACHIEVEMENT_TRP10                           10
#define ACHIEVEMENT_TRP11                           11
#define ACHIEVEMENT_TRP12                           12
#define ACHIEVEMENT_TRP13                           13
#define ACHIEVEMENT_TRP14                           14
#define ACHIEVEMENT_TRP15                           15
#define ACHIEVEMENT_TRP16                           16
#define ACHIEVEMENT_TRP17                           17
#define ACHIEVEMENT_TRP18                           18
#define ACHIEVEMENT_TRP19                           19
#define ACHIEVEMENT_TRP20                           20
#define ACHIEVEMENT_TRP21                           21
#define ACHIEVEMENT_TRP22                           22
#define ACHIEVEMENT_TRP23                           23
#define ACHIEVEMENT_TRP24                           24
#define ACHIEVEMENT_TRP25                           25
#define ACHIEVEMENT_TRP26                           26
#define ACHIEVEMENT_TRP27                           27
#define ACHIEVEMENT_TRP28                           28
#define ACHIEVEMENT_TRP29                           29
#define ACHIEVEMENT_TRP30                           30
#define ACHIEVEMENT_TRP31                           31
#define ACHIEVEMENT_TRP32                           32
#define ACHIEVEMENT_TRP33                           33
#define ACHIEVEMENT_TRP34                           34
#define ACHIEVEMENT_TRP35                           35
#define ACHIEVEMENT_TRP36                           36

//
// AvatarAssetAward ids
//


//
// Stats view ids
//
// These are used in the dwViewId member of the XUSER_STATS_SPEC structure
// passed to the XUserReadStats* and XUserCreateStatsEnumerator* functions.
//

// Skill leaderboards for ranked game modes

#define STATS_VIEW_SKILL_RANKED_MENU                0xFFFF0000

// Skill leaderboards for unranked (standard) game modes

#define STATS_VIEW_SKILL_STANDARD_MENU              0xFFFE0000

// Title defined leaderboards


//
// Stats view column ids
//
// These ids are used to read columns of stats views.  They are specified in
// the rgwColumnIds array of the XUSER_STATS_SPEC structure.  Rank, rating
// and gamertag are not retrieved as custom columns and so are not included
// in the following definitions.  They can be retrieved from each row's
// header (e.g., pStatsResults->pViews[x].pRows[y].dwRank, etc.).
//

//
// Matchmaking queries
//
// These values are passed as the dwProcedureIndex parameter to
// XSessionSearch to indicate which matchmaking query to run.
//


//
// Gamer pictures
//
// These ids are passed as the dwPictureId parameter to XUserAwardGamerTile.
//


//
// Strings
//
// These ids are passed as the dwStringId parameter to XReadStringsFromSpaFile.
//

#define SPASTRING_MENU                              22
#define SPASTRING_PRESENCE_INTHEMENUS_NAME          147
#define SPASTRING_PRESENCE_PLAYING_JUNGLE_A_NAME    149
#define SPASTRING_PRESENCE_INWORLDMAP_NAME          150
#define SPASTRING_PRESENCE_PLAYING_JUNGLE_B_NAME    157
#define SPASTRING_PRESENCE_PLAYING_MUSIC_A_NAME     158
#define SPASTRING_PRESENCE_PLAYING_MUSIC_B_NAME     159
#define SPASTRING_PRESENCE_PLAYING_FOOD_A_NAME      160
#define SPASTRING_PRESENCE_PLAYING_FOOD_B_NAME      161
#define SPASTRING_PRESENCE_PLAYING_OCEAN_A_NAME     162
#define SPASTRING_PRESENCE_PLAYING_OCEAN_B_NAME     163
#define SPASTRING_PRESENCE_PLAYING_MOUNTAIN_A_NAME  164
#define SPASTRING_PRESENCE_PLAYING_MOUNTAIN_B_NAME  165
#define SPASTRING_PRESENCE_PLAYING_DEATH_NAME       166
#define SPASTRING_PRESENCE_PLAYING_HOME_NAME        167
#define SPASTRING_PRESENCE_NOTPLAYING_NAME          276
#define SPASTRING_TRP01TITLE                        601
#define SPASTRING_TRP01DESC                         602
#define SPASTRING_TRP01HOWTO                        603
#define SPASTRING_TRP02TITLE                        604
#define SPASTRING_TRP02DESC                         605
#define SPASTRING_TRP02HOWTO                        606
#define SPASTRING_TRP03TITLE                        607
#define SPASTRING_TRP03DESC                         608
#define SPASTRING_TRP03HOWTO                        609
#define SPASTRING_TRP04TITLE                        610
#define SPASTRING_TRP04DESC                         611
#define SPASTRING_TRP04HOWTO                        612
#define SPASTRING_TRP05TITLE                        613
#define SPASTRING_TRP05DESC                         614
#define SPASTRING_TRP05HOWTO                        615
#define SPASTRING_TRP06TITLE                        616
#define SPASTRING_TRP06DESC                         617
#define SPASTRING_TRP06HOWTO                        618
#define SPASTRING_TRP07TITLE                        619
#define SPASTRING_TRP07DESC                         620
#define SPASTRING_TRP07HOWTO                        621
#define SPASTRING_TRP08TITLE                        622
#define SPASTRING_TRP08DESC                         623
#define SPASTRING_TRP08HOWTO                        624
#define SPASTRING_TRP09TITLE                        625
#define SPASTRING_TRP09DESC                         626
#define SPASTRING_TRP09HOWTO                        627
#define SPASTRING_TRP10TITLE                        628
#define SPASTRING_TRP10DESC                         629
#define SPASTRING_TRP10HOWTO                        630
#define SPASTRING_TRP11TITLE                        631
#define SPASTRING_TRP11DESC                         632
#define SPASTRING_TRP11HOWTO                        633
#define SPASTRING_TRP12TITLE                        634
#define SPASTRING_TRP12DESC                         635
#define SPASTRING_TRP12HOWTO                        636
#define SPASTRING_TRP13TITLE                        637
#define SPASTRING_TRP13DESC                         638
#define SPASTRING_TRP13HOWTO                        639
#define SPASTRING_TRP14TITLE                        640
#define SPASTRING_TRP14DESC                         641
#define SPASTRING_TRP14HOWTO                        642
#define SPASTRING_TRP15TITLE                        643
#define SPASTRING_TRP15DESC                         644
#define SPASTRING_TRP15HOWTO                        645
#define SPASTRING_TRP16TITLE                        646
#define SPASTRING_TRP16DESC                         647
#define SPASTRING_TRP16HOWTO                        648
#define SPASTRING_TRP17TITLE                        649
#define SPASTRING_TRP17DESC                         650
#define SPASTRING_TRP17HOWTO                        651
#define SPASTRING_TRP18TITLE                        652
#define SPASTRING_TRP18DESC                         653
#define SPASTRING_TRP18HOWTO                        654
#define SPASTRING_TRP19TITLE                        655
#define SPASTRING_TRP19DESC                         656
#define SPASTRING_TRP19HOWTO                        657
#define SPASTRING_TRP20TITLE                        658
#define SPASTRING_TRP20DESC                         659
#define SPASTRING_TRP20HOWTO                        660
#define SPASTRING_TRP21TITLE                        661
#define SPASTRING_TRP21DESC                         662
#define SPASTRING_TRP21HOWTO                        663
#define SPASTRING_TRP22TITLE                        664
#define SPASTRING_TRP22DESC                         665
#define SPASTRING_TRP22HOWTO                        666
#define SPASTRING_TRP23TITLE                        667
#define SPASTRING_TRP23DESC                         668
#define SPASTRING_TRP23HOWTO                        669
#define SPASTRING_TRP24TITLE                        670
#define SPASTRING_TRP24DESC                         671
#define SPASTRING_TRP24HOWTO                        672
#define SPASTRING_TRP25TITLE                        673
#define SPASTRING_TRP25DESC                         674
#define SPASTRING_TRP25HOWTO                        675
#define SPASTRING_TRP26TITLE                        676
#define SPASTRING_TRP26DESC                         677
#define SPASTRING_TRP26HOWTO                        678
#define SPASTRING_TRP27TITLE                        679
#define SPASTRING_TRP27DESC                         680
#define SPASTRING_TRP27HOWTO                        681
#define SPASTRING_TRP28TITLE                        682
#define SPASTRING_TRP28DESC                         683
#define SPASTRING_TRP28HOWTO                        684
#define SPASTRING_TRP29TITLE                        685
#define SPASTRING_TRP29DESC                         686
#define SPASTRING_TRP29HOWTO                        687
#define SPASTRING_TRP30TITLE                        688
#define SPASTRING_TRP30DESC                         689
#define SPASTRING_TRP30HOWTO                        690
#define SPASTRING_TRP31TITLE                        691
#define SPASTRING_TRP31DESC                         692
#define SPASTRING_TRP31HOWTO                        693
#define SPASTRING_TRP32TITLE                        694
#define SPASTRING_TRP32DESC                         695
#define SPASTRING_TRP32HOWTO                        696
#define SPASTRING_TRP33TITLE                        697
#define SPASTRING_TRP33DESC                         698
#define SPASTRING_TRP33HOWTO                        699
#define SPASTRING_TRP34TITLE                        700
#define SPASTRING_TRP34DESC                         701
#define SPASTRING_TRP34HOWTO                        702
#define SPASTRING_TRP35TITLE                        703
#define SPASTRING_TRP35DESC                         704
#define SPASTRING_TRP35HOWTO                        705
#define SPASTRING_TRP36TITLE                        706
#define SPASTRING_TRP36DESC                         707
#define SPASTRING_TRP36HOWTO                        708


#ifdef __cplusplus
}
#endif

#endif // __RAYMAN_ORIGINS_SPA_H__


