#include "application_framework.h"

namespace ITF
{
	class ApplicationFramework_WII : public ApplicationFramework
	{
	public:

		ApplicationFramework_WII();
		virtual ~ApplicationFramework_WII() {};
		
		//int init(const String& _szAdapter = "", const String& _cmdLine = "");
		
		String      getVersion()const;
		//void        update();
		//void        destroy();

	protected:

        //virtual void drawAll();

        virtual bbool preInit(CommandArgs &_args);

		bbool	checkForExit();
		void	initSystemAndGraphics(const String& _szAdapter);
		void	mountFileDevice();

        bbool hasToFastExit() const { return btrue; }

#ifndef ITF_FINAL
        void	registerPlugins(u16 _port);
        void	unregisterPlugins();

        bbool   isEngineMonitorConnected();
#endif //ITF_FINAL

        u32     m_exitAction; 
	};
}