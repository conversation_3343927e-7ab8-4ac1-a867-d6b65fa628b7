#ifndef ERRORS_H
#define ERRORS_H


#include "core/enginecommon.h"

#ifndef _ITF_CORETYPES_H_
#include "core/types.h"
#endif //_ITF_CORETYPES_H_

#ifndef ITF_FINAL
void Wii_InstallErrorHandler();

#define DBG_DVDBUFSIZE  128*1024
#define STACK_DEPTH     24
#define NAME_SIZE       256
#define MODULENAME_SIZE 64

struct ErrorHandler_S {
    char debugFileBuff[DBG_DVDBUFSIZE] __attribute__ ((aligned(32)));
	unsigned char RunStack[16 * 1024] __attribute__ ((aligned(32)));
    DVDFileInfo debugFileHandler;
	bool Decoding;
    int  ErrorID;
    char stackFonctions[STACK_DEPTH][NAME_SIZE];
    char stackModules[STACK_DEPTH][MODULENAME_SIZE];
	u32  stackOffsets [STACK_DEPTH];
    u32  stack[STACK_DEPTH];
    u32  debugBuffSwitch;
    u32  DebugFileTell;
    u32  DebugFileTellHeader;
    u32  DebugFileTellLastRequested;
    u32  DebugFileTellLastProcessed;

    // String search data
    char strSearch[128];
    char *strSearchPtr;
    u32  strSearchLen;
    // Name search data
    char strTmp    [2048];
    char funcName  [2048];
    char *pstrTmp;
    u32  csAdd;
    u32  cSize;
    u32  cvAdd;
    u32  cvAlign;
    // Display nfo
    /*float DscaleX;
    float DscaleY;
    int DoffsetX;
    int DoffsetY;*/
};


// the DB library is deprecated (not working) as of SDK 3.3 
// - use the following undocumented function in place of DBIsDebuggerPresent()
//   also, there seems to be no conflicts anymore between our 
//   exception handler and the CW debugger, so we could probably avoid 
//   using this function
extern "C" BOOL __OSIsDebuggerPresent();

//Error Display
extern void WiiErrorHandlerFiber(OSError _error, OSContext* context, u32 dsisr, u32 dar);

extern volatile int strSearchCount; // -1 failure
extern OSTime InstallTime;
extern OSTime ExceptionTime; 

// Error Handler
extern volatile u32 ErrorHandlerCount;
extern ErrorHandler_S *pErrorHandlerS;

extern void WiiErrorHandler(OSError    _error,
                            OSContext* context,
                            u32        dsisr,
                            u32        dar);

// Map reader
void ParseOneMapFile(u32 (*stack)[STACK_DEPTH],
              char (*stackFonctions)[STACK_DEPTH][NAME_SIZE],
              char (*stackModules)[STACK_DEPTH][MODULENAME_SIZE],
              char *MapFileName,
			  u32 addressOffset);

#else // ITF_FINAL
void Wii_InstallRetailErrorHandler();
#endif // ITF_FINAL

#endif // ERRORS_H