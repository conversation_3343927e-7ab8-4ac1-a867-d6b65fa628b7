#include "precompiled_techno.h"
#include <variant>

#include <objbase.h>

#include "core/itfstring.h"
#include "application_framework_Win.h"
#include "core/memory/threadAllocatorStacker.h"
#include "core/file/filepath.h"

#ifdef  NDEBUG
#define ENABLE_CRASHHANDLER 1
#endif //NDEBUG

#ifdef ENABLE_CRASHHANDLER
#include "client/windows/handler/exception_handler.h"
#include "client/windows/crash_generation/crash_generation_server.h"

#ifndef _ITF_DEBUGINFO_H_
#include "engine/debug/debugInfo.h"
#endif //_ITF_DEBUGINFO_H_

#ifndef _ITF_DIRECTORY_H_
#include "core/file/Directory.h"
#endif //_ITF_DIRECTORY_H_

#ifndef _ITF_STACKWALKER_H_
#include "core/StackWalker.h"
#endif //_ITF_STACKWALKER_H_

#ifndef _ITF_REPORTISSUEUTILS_H_
#include "engine/debug/reportIssueUtils.h"
#endif //_ITF_REPORTISSUEUTILS_H_


#endif //ENABLE_CRASHHANDLER

#ifdef  ITF_TRACKING_MEMORY_ENABLE
//add this to enable the memory tracking
ITF::threadAllocatorTrackerScope useMemoryTracking;
#endif  //ITF_TRACKING_MEMORY_ENABLE

#ifdef ENABLE_CRASHHANDLER

TCHAR szApplicationDirectory[MAX_PATH] = L"";

DWORD clVersion = 0;

BOOL DoesPathExist(const TCHAR *path_name) {
    DWORD flags = GetFileAttributes(path_name);
    if (flags == INVALID_FILE_ATTRIBUTES) {
        return FALSE;
    }
    return TRUE;
}

const char kSuccessIndicator[] = "success";
const char kFailureIndicator[] = "failure";

void dumpCrash(CONTEXT* pContext,const wchar_t* _fullpath,const wchar_t* _shortname)
{
    using namespace ITF;

#ifndef ITF_FINAL
    //There is a crash... avoid to use engine thingie just in case
    StackWalker stackWalker;
    stackWalker.initialize();

    const u32 MAX_STACK_DEPTH = 62;
    DWORD machineType = 0;

    wchar_t* wszProcessor = _wgetenv(L"PROCESSOR_ARCHITECTURE");   
    if (wszProcessor)
    {
        if ((!wcscmp(L"EM64T", wszProcessor)) ||!wcscmp(L"AMD64", wszProcessor))
        {
            machineType = IMAGE_FILE_MACHINE_AMD64;
        }
        else if (!wcscmp(L"x86", wszProcessor))
        {
            machineType = IMAGE_FILE_MACHINE_I386;
        }
    }

    StackWalker::ProcessAddress m_addresses[MAX_STACK_DEPTH];

    ITF_MemSet(m_addresses,0,sizeof(m_addresses));

    // Walk through the stack frames.
    HANDLE hProcess = GetCurrentProcess();
    HANDLE hThread = GetCurrentThread();
    // Initialize stack frame
    STACKFRAME64 sf;
    memset(&sf, 0, sizeof(STACKFRAME));

#if defined(_WIN64)
    sf.AddrPC.Offset = pContext->Rip;
    sf.AddrStack.Offset = pContext->Rsp;
    sf.AddrFrame.Offset = pContext->Rbp;
#elif defined(WIN32)
    sf.AddrPC.Offset = pContext->Eip;
    sf.AddrStack.Offset = pContext->Esp;
    sf.AddrFrame.Offset = pContext->Ebp;
#endif
    sf.AddrPC.Mode      = AddrModeFlat;
    sf.AddrStack.Mode   = AddrModeFlat;
    sf.AddrFrame.Mode   = AddrModeFlat;

    // Walk the stack.
    
    size_t depth = 0;
    while (depth < MAX_STACK_DEPTH && StackWalk64(machineType, hProcess, hThread, 
        &sf, pContext, NULL, SymFunctionTableAccess64, 
        SymGetModuleBase64, NULL))
    {
        if (sf.AddrFrame.Offset != 0) 
        {
            m_addresses[depth++] = sf.AddrPC.Offset;
        }
    }    

    TCHAR nameDumpTxt[MAX_PATH];
    swprintf_s( nameDumpTxt,MAX_PATH,L"%s/dumpInfo.txt",szApplicationDirectory);

    FILE * fDumpFile = _wfopen(nameDumpTxt,L"wt");

    if (fDumpFile)
    {
        #define MAX_CHAR_INFO 512
        char lineInfo[MAX_CHAR_INFO];
        
        char path_ansi[MAX_CHAR_INFO];
        wcstombs( path_ansi, _fullpath, MAX_CHAR_INFO );

        char name_ansi[MAX_CHAR_INFO];
        wcstombs( name_ansi, _shortname, MAX_CHAR_INFO );

        ProcessAddressDescriptor desc;
       
        u32 index =0;
        while (index < MAX_STACK_DEPTH)
        {
            u64 addr = m_addresses[index++];
            if (addr)
            {
                stackWalker.fillDescriptor(addr,desc);

                if (desc.m_line>0)
                {
                    sprintf_s(lineInfo,MAX_CHAR_INFO,"%s(%d) : %s\n",desc.m_filename,desc.m_line,desc.m_symbolName);
                    fputs(lineInfo,fDumpFile);
                }
            }
        }

        if (DEBUGINFO)
            DEBUGINFO->dump(fDumpFile);

        fclose(fDumpFile);

		if(SYSTEM_ADAPTER->bProcessDumpOnCrash())
			reportIssueUtils::generateCrashDump(szApplicationDirectory,_fullpath,_shortname,clVersion);
    }
#endif //!ITF_FINAL
}

bool MinidumpWrittenCallback(const wchar_t* dump_path, 
                             const wchar_t* minidump_id,
                             void* context,
                             EXCEPTION_POINTERS* exinfo,
                             MDRawAssertionInfo* assertion,
                             bool succeeded)
{
    using namespace ITF;

    if (succeeded && DoesPathExist(dump_path))
    {
        fprintf(stderr, kSuccessIndicator);
    }
    else
    {
        fprintf(stderr, kFailureIndicator);
    }

    // If we don't flush, the output doesn't get sent before
    // this process dies.
    fflush(stderr);

    wchar_t path[MAX_PATH];
    swprintf_s(path, MAX_PATH, L"%s\\%s.dmp", dump_path, minidump_id);

    wchar_t info[MAX_PATH];
    swprintf_s(info, MAX_PATH, L"Dump information location: %s", path);


#if defined(EXPORT_ASSERTS)
    AssertManager::close();
#endif //defined(EXPORT_ASSERTS)

    dumpCrash(exinfo->ContextRecord,dump_path,minidump_id);
    ITF_ERROR("Application crashed\n%ls\n%ls", path, info);

    return succeeded;
}

google_breakpad::ExceptionHandler* handler =  NULL;
#endif //ENABLE_CRASHHANDLER

namespace ITF
{

    static void callMain(const String& cmdLine)
    {
        ITF::Thread::SetCurrentThreadName("Main");

        //ITF::threadAllocatorTracker::createInstance();

#if defined(EXPORT_ASSERTS)
        ITF::AssertManager::init();
#endif // EXPORT_ASSERTS

        wchar_t strExePath[MAX_PATH];
        const DWORD uSize = GetModuleFileName(NULL, strExePath, MAX_PATH);
#ifndef ITF_FINAL
        if (!IsDebuggerPresent()) //use the working directory set in your ide
#endif //ITF_FINAL
        {
            if (uSize > 0)
            {
                for (int i = (uSize - 1); i > 0; --i)
                {
                    if ('\\' == strExePath[i])
                    {
                        wchar_t save = strExePath[i];
                        strExePath[i] = '\0';
                        ITF_VERIFY_MSG(SetCurrentDirectory(strExePath) == TRUE, "Could not set the current directory");
                        strExePath[i] = save;
                        break;
                    }
                }
            }
        }

#ifdef ENABLE_CRASHHANDLER

        TCHAR szDumpPath[MAX_PATH];
        ::GetCurrentDirectory(MAX_PATH, szApplicationDirectory);
        swprintf_s(szDumpPath, MAX_PATH, L"%s\\dumpCrash", szApplicationDirectory);

        ITF::Directory::create((ITF::u16*)szDumpPath);

        DWORD dwDummy;
        const DWORD dwSize = GetFileVersionInfoSize(strExePath, &dwDummy);

        LPBYTE lpVersionInfo = new BYTE[dwSize];
        GetFileVersionInfo(strExePath, 0, dwSize, lpVersionInfo);

        UINT uLen;
        VS_FIXEDFILEINFO* lpFfi;
        VerQueryValue(lpVersionInfo, L"\\", (LPVOID*)&lpFfi, &uLen);

        const DWORD dwFileVersionLS = lpFfi->dwFileVersionLS;
        delete[] lpVersionInfo;

        clVersion = LOWORD(dwFileVersionLS);
        ITF::String ret("BETA");
        if (clVersion > 0)
            ret.setTextFormat("CL%d", clVersion);

        reportIssueUtils::clVersion = clVersion;

        if (!IsDebuggerPresent())
        {
            bool useFullDump = true;
            MINIDUMP_TYPE dumpType = useFullDump ? MiniDumpWithFullMemory : MiniDumpNormal;

            handler = new google_breakpad::ExceptionHandler(szDumpPath, NULL, &MinidumpWrittenCallback, NULL,
                google_breakpad::ExceptionHandler::HANDLER_ALL,
                dumpType, (HANDLE)NULL, NULL, ret.wcharCStr());
        }
#endif //ENABLE_CRASHHANDLER

        // Used to show context menu enhanced extension : http://www.ureader.com/message/821299.aspx
        // $GB 2025/25/02: check that it is necessary for non editor version (also CoUninitialize below)
        const int r = ::CoInitialize(NULL);
        ITF_VERIFY(r == S_OK);

        ITF::ApplicationFramework_Win applicationFramework;

        ITF::String strExeDir = ITF::FilePath::getDirectory((ITF::u16*)strExePath);

        if (applicationFramework.init("directx", cmdLine, strExeDir))
        {
            applicationFramework.update();
        }
        applicationFramework.destroy();

        if (r == S_OK)
            ::CoUninitialize();

#ifdef ENABLE_CRASHHANDLER
        delete handler;
#endif //ENABLE_CRASHHANDLER

#if defined(EXPORT_ASSERTS)
        ITF::AssertManager::cleanup();
#endif // EXPORT_ASSERTS
    }

    class RegistryAccessor
    {
    public:

        enum OpenMode
        {
            Open, // Open an existing key, the Key is invalid if not existing
            OpenOrCreate, // Open an existing key or create it if not existing
        };

        enum AccessFlag : u32
        {
            Read = 1,
            Write = 2,
            ReadWrite = static_cast<AccessFlag>(Read | Write),
        };

        enum BaseRegistryKey : u32
        {
            ClassesRoot,
            CurrentConfig,
            CurrentUser,
            LocalMachine,
            Users,
        };

        class Key
        {
        public:

            Key() = delete;
            Key(const Key&) = delete;
            Key& operator = (const Key&) = delete;

            explicit Key(BaseRegistryKey _baseKey, const String& _subKey, OpenMode openMode = OpenOrCreate, AccessFlag _accessMode = ReadWrite)
            {
                HKEY baseKey = baseRegistryKeyToHKey(_baseKey);
                REGSAM accessMode = accessModeToRegSam(_accessMode);

                HKEY hKey = nullptr;
                if (RegOpenKeyExW(baseKey, _subKey.wcharCStr(), 0, accessMode, &hKey) == ERROR_SUCCESS)
                {
                    m_hKey = hKey;
                }
                else
                {
                    if (openMode == OpenOrCreate)
                    {
                        if (RegCreateKeyExW(baseKey, _subKey.wcharCStr(), 0, NULL, 0, accessMode, NULL, &hKey, NULL) == ERROR_SUCCESS)
                        {
                            m_hKey = hKey;
                        }
                    }
                }
            }

            bool isValid() const { return m_hKey != nullptr; }

            ~Key()
            {
                if (isValid())
                    RegCloseKey(m_hKey);
            }

            bool setValue(const String& _valueName, const String& _value) const
            {
                if (isValid())
                    return RegSetValueExW(m_hKey, _valueName.wcharCStr(), 0, REG_SZ, reinterpret_cast<CONST BYTE*>(_value.wcharCStr()), _value.getLen() * sizeof(wchar_t)) == ERROR_SUCCESS;
                return false;
            }

            bool setValue(const String& _valueName, u32 _value) const
            {
                if (isValid())
                    return RegSetValueExW(m_hKey, _valueName.wcharCStr(), 0, REG_DWORD, reinterpret_cast<CONST BYTE*>(&_value), sizeof(u32)) == ERROR_SUCCESS;
                return false;
            }

            bool getValue(const String& valueName, String& value) const
            {
                if (isValid())
                {
                    DWORD size = 0;
                    DWORD type = 0;
                    // First call to get the size of the buffer
                    if (RegQueryValueExW(m_hKey, valueName.wcharCStr(), NULL, &type, NULL, &size) == ERROR_SUCCESS)
                    {
                        if (type == REG_SZ)
                        {
                            // Allocate buffer with the required size
                            Vector<wchar_t> buffer(size / sizeof(wchar_t));

                            // Second call to get the actual value
                            if (RegQueryValueExW(m_hKey, valueName.wcharCStr(), NULL, NULL, reinterpret_cast<LPBYTE>(buffer.data()), &size) == ERROR_SUCCESS)
                            {
                                value.setText(buffer.data());
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            bool getValue(const String& _valueName, u32& _value) const
            {
                if (isValid())
                {
                    DWORD size = sizeof(u32);
                    DWORD type = 0;
                    DWORD value = _value;
                    if (RegQueryValueExW(m_hKey, _valueName.wcharCStr(), NULL, &type, reinterpret_cast<LPBYTE>(&value), &size) == ERROR_SUCCESS)
                    {
                        if (type == REG_DWORD)
                        {
                            _value = value;
                            return true;
                        }
                    }
                }
                return false;
            }

            template <typename T>
            T getOrSetDefault(const String& valueName, const T& defaultValue) const
            {
                T result = defaultValue;
                if (!getValue(valueName, result))
                {
                    setValue(valueName, result);
                }
                return result;
            }

        private:

            static HKEY baseRegistryKeyToHKey(BaseRegistryKey baseKey)
            {
                switch (baseKey)
                {
                case ClassesRoot:
                    return HKEY_CLASSES_ROOT;
                case CurrentConfig:
                    return HKEY_CURRENT_CONFIG;
                case CurrentUser:
                    return HKEY_CURRENT_USER;
                case LocalMachine:
                    return HKEY_LOCAL_MACHINE;
                case Users:
                    return HKEY_USERS;
                default:
                    return nullptr;
                }
            }

            static REGSAM accessModeToRegSam(AccessFlag accessMode)
            {
                REGSAM samDesired = 0;
                if (accessMode & Read)
                {
                    samDesired |= KEY_READ;
                }
                if (accessMode & Write)
                {
                    samDesired |= KEY_WRITE;
                }
                return samDesired;
            }

            HKEY m_hKey = nullptr;
        };
    };

    static String getRootFromCurrentModule()
    {
        Vector<wchar_t> exePath;
        exePath.resize(64 * 1024, L'\0');
        ::GetModuleFileNameW(NULL, exePath.data(), exePath.size());

        String strExePath = exePath.data();

        i32 lastSlash = strExePath.rfind(L'/');
        i32 lastBackSlash = strExePath.rfind(L'\\');
        i32 lastSlashOrBackSlash = std::max(lastSlash, lastBackSlash);

        return strExePath.substr(0, lastSlashOrBackSlash + 1);
    }

    class RegistryEntry
    {
    public:
        RegistryEntry() = default;

        template <typename T>
        RegistryEntry(const wchar_t* _registryName, const wchar_t* _commandLineName, const T& _value)
            : m_registryName(_registryName)
            , m_commandLineName(_commandLineName)
            , m_value(_value)
        {
        }

        String getRegistryName() const { return m_registryName; }
        String getCommandLineName() const { return m_commandLineName; }
        std::variant<String, u32> getValue() const { return m_value; }

        void fetch(const RegistryAccessor::Key& key)
        {
            visit(
                [&](auto& value)
                {
                    value = key.getOrSetDefault(m_registryName, value);
                },
                m_value);
        }

        void addToCommandLine(String& cmdLine) const
        {
            if (!cmdLine.isEmpty() && cmdLine.wcharCStr()[cmdLine.getLen() - 1] != L';')
                cmdLine += L";";

            visit(
                [&](auto& value)
                {
                    cmdLine += buildCmdLineAssignment(value);
                },
                m_value);
        }

    private:

        String buildCmdLineAssignment(String value) const
        {
            String strToAdd;
            strToAdd.setStringFormat("%s=%s", m_commandLineName.wcharCStr(), value.wcharCStr());
            return strToAdd;
        }

        String buildCmdLineAssignment(u32 value) const
        {
            String strValue;
            strValue.setTextFormat("%u", value);
            return buildCmdLineAssignment(strValue);
        }

        String m_registryName;
        String m_commandLineName;
        std::variant<String, u32> m_value;
    };

    class RegistryEntryCollection
    {
    public:
        RegistryEntryCollection() = default;

        void push_back(const RegistryEntry& entry)
        {
            m_entries.push_back(entry);
        }

        template <typename T>
        T getValueOrDefaultFromCommandLineName(const wchar_t* cmdLineName, const T& defaultValue) const
        {
            auto itFound = std::find_if(m_entries.begin(), m_entries.end(), [&](auto& entry)
                {
                    return entry.getCommandLineName() == cmdLineName;
                });
            if (itFound != m_entries.end())
            {
                T* val = std::get_if<T>(&itFound->getValue());
                if (val != nullptr)
                    return *val;
            }
            return defaultValue;
        }

        static RegistryEntryCollection buildForGame()
        {
            RegistryEntryCollection entries;

            // Kept as string for compatibility
            String widthStr;
            widthStr.setTextFormat("%d", ::GetSystemMetrics(SM_CXSCREEN));
            entries.push_back({ L"ScreenWidth", L"width", widthStr });

            String heightStr;
            heightStr.setTextFormat("%d", ::GetSystemMetrics(SM_CYSCREEN));
            entries.push_back({ L"ScreenHeight", L"height", widthStr });

            entries.push_back({ L"FullScreen", L"fullscreen", u32(1) });
            entries.push_back({ L"Language", L"language", u32(ITF_LANGUAGE_ENGLISH) });

            return entries;
        }

        void fetch()
        {
            RegistryAccessor::Key keyToSettings{ RegistryAccessor::CurrentUser, L"Software\\Ubisoft\\RaymanOrigins\\Settings\\" };

            if (!keyToSettings.isValid())
                return;

            for (auto& entry : m_entries)
            {
                entry.fetch(keyToSettings);
            }
        }

        void addToCommandLine(String& cmdLine) const
        {
            for (auto& entry : m_entries)
            {
                entry.addToCommandLine(cmdLine);
            }
        }

    private:

        Vector<RegistryEntry> m_entries;
    };


    class AlreadyRunningMessageHandler
    {
    public:
        explicit AlreadyRunningMessageHandler()
            : m_langMessage
            {
                { ITF_LANGUAGE_ENGLISH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x69,0x73,0x20,0x61,0x6c,0x72,0x65,0x61,0x64,0x79,0x20,0x72,0x75,0x6e,0x6e,0x69,0x6e,0x67,0x2e,0x0 })},
                { ITF_LANGUAGE_FRENCH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x65,0x73,0x74,0x20,0x64,0xe9,0x6a,0xe0,0x20,0x65,0x6e,0x20,0x63,0x6f,0x75,0x72,0x73,0x20,0x64,0x27,0x65,0x78,0xe9,0x63,0x75,0x74,0x69,0x6f,0x6e,0x2e,0x0 })},
                { ITF_LANGUAGE_JAPANESE, buildString({ 0x30ec,0x30a4,0x30de,0x30f3,0x20,0x30aa,0x30ea,0x30b8,0x30f3,0x306f,0x8d77,0x52d5,0x3057,0x3066,0x3044,0x307e,0x3059,0x3002,0x0 })},
                { ITF_LANGUAGE_GERMAN, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x6c,0xe4,0x75,0x66,0x74,0x20,0x62,0x65,0x72,0x65,0x69,0x74,0x73,0x2e,0x0 })},
                { ITF_LANGUAGE_SPANISH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x79,0x61,0x20,0x65,0x73,0x74,0xe1,0x20,0x65,0x6a,0x65,0x63,0x75,0x74,0xe1,0x6e,0x64,0x6f,0x73,0x65,0x2e,0x0 })},
                { ITF_LANGUAGE_ITALIAN, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0xe8,0x20,0x67,0x69,0xe0,0x20,0x69,0x6e,0x20,0x65,0x73,0x65,0x63,0x75,0x7a,0x69,0x6f,0x6e,0x65,0x2e,0x0 })},
                { ITF_LANGUAGE_KOREAN, buildString(L"Rayman Origins is already running.")},
                { ITF_LANGUAGE_TRADITIONALCHINESE, buildString(L"Rayman Origins is already running.")},
                { ITF_LANGUAGE_PORTUGUESE, buildString({ 0x4f,0x20,0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x69,0x73,0x20,0x6a,0xe1,0x20,0x65,0x73,0x74,0xe1,0x20,0x73,0x65,0x6e,0x64,0x6f,0x20,0x65,0x78,0x65,0x63,0x75,0x74,0x61,0x64,0x6f,0x2e,0x0 })},
                { ITF_LANGUAGE_SIMPLIFIEDCHINESE, buildString(L"Rayman Origins is already running.")},
                { ITF_LANGUAGE_POLISH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x6a,0x65,0x73,0x74,0x20,0x6a,0x75,0x17c,0x20,0x75,0x72,0x75,0x63,0x68,0x6f,0x6d,0x69,0x6f,0x6e,0x79,0x2e,0x0 })},
                { ITF_LANGUAGE_RUSSIAN, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x443,0x436,0x435,0x20,0x437,0x430,0x43f,0x443,0x449,0x435,0x43d,0x430,0x2e,0x0 })},
                { ITF_LANGUAGE_DUTCH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x64,0x72,0x61,0x61,0x69,0x74,0x20,0x61,0x6c,0x2e,0x0 })},
                { ITF_LANGUAGE_CZECH, buildString({ 0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x6a,0x69,0x17e,0x20,0x62,0x11b,0x17e,0xed,0x2e,0x0 })},
                { ITF_LANGUAGE_HUNGARIAN, buildString({ 0x41,0x20,0x52,0x61,0x79,0x6d,0x61,0x6e,0x20,0x4f,0x72,0x69,0x67,0x69,0x6e,0x73,0x20,0x6d,0xe1,0x72,0x20,0x66,0x75,0x74,0x2e,0x0 })},
            }
        {
        }

        String getMessage(ITF_LANGUAGE _lang) const
        {
            auto it = m_langMessage.find(_lang);
            if (it == m_langMessage.end())
            {
                it = m_langMessage.find(_lang);
            }
            if (it != m_langMessage.end())
            {
                return it->second;
            }
            return L"Rayman Origins is already running.";
        }

    private:
        static String buildString(const wchar_t* _message)
        {
            return String{ _message };
        }
        static String buildString(std::initializer_list<wchar_t> _messageInNumericForm)
        {
            return String{ _messageInNumericForm.begin() };
        }

        ITF::Map<ITF_LANGUAGE, String> m_langMessage;
    };

    static bool isGameAlreadyOpened()
    {
        // Use a OS global mutex
        const wchar_t mutexName[] = L"Rayman Origins Run Checker";
        if (::OpenMutexW(MUTEX_ALL_ACCESS, false, mutexName) != nullptr)
            return true;
        ::CreateMutexW(NULL, true, mutexName);
        return false;
    }

    static void displayAnotherInstanceOpenedMessage(ITF_LANGUAGE langID)
    {
        AlreadyRunningMessageHandler messageHandler;
        ::MessageBoxW(NULL, messageHandler.getMessage(langID).wcharCStr(), L"Rayman Origins", MB_OK | MB_TOPMOST);
    }


    // mainWithoutCatch exist because we cannot use __try __catch blocks in a function that uses C++ exceptions
    static void mainWithoutCatch(const wchar_t* pCmdLine)
    {
        String cmdLine = pCmdLine;
#if !defined(ITF_SUPPORT_EDITOR)

        auto registryEntries = RegistryEntryCollection::buildForGame();
        registryEntries.fetch(); // Fetch the values from the registry

        if (cmdLine.strstr(reinterpret_cast<const u16*>(L"root=")) == nullptr)
        {
            String root = getRootFromCurrentModule();
            registryEntries.push_back({ L"Root", L"root", root + L"GameData\\" });
        }

        registryEntries.addToCommandLine(cmdLine);

        if (isGameAlreadyOpened())
        {
            ITF_LANGUAGE langID = static_cast<ITF_LANGUAGE>(registryEntries.getValueOrDefaultFromCommandLineName(L"language", u32(ITF_LANGUAGE_ENGLISH)));
            displayAnotherInstanceOpenedMessage(langID);
        }
#endif
        callMain(cmdLine);
    }
} // namespace ITF

static int filter(unsigned int code, struct _EXCEPTION_POINTERS* ep)
{
    return 0;
}

#ifdef ENABLE_CRASHHANDLER
#define TRY         __try
#define EXCEPT      __except
#else
#define TRY         {}
#define EXCEPT(a)     {}
#endif //ENABLE_CRASHHANDLER

int WINAPI wWinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, PWSTR pCmdLine, int nCmdShow)
{
    TRY
    {
        ITF::mainWithoutCatch(pCmdLine);
    }
    EXCEPT(filter(GetExceptionCode(), GetExceptionInformation()))
    {
    }
    return 0;
}
