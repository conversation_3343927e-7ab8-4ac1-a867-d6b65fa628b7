#ifndef APPLICATIONFRAMEWORK_PS5_H_
#define APPLICATIONFRAMEWORK_PS5_H_

#pragma once

#include "application_framework.h"

namespace ITF
{
    class ApplicationFramework_PS5 : public ApplicationFramework
    {
    public:

        ApplicationFramework_PS5();
        virtual ~ApplicationFramework_PS5() = default;

        bbool hasStartupSucceed() const { return m_startupSuccess; }
        const char * getRootPath() const { return m_rootPath.cStr(); }

        void waitUntilQuitMessage();

        virtual void  applyExit() override;

    protected:
        using Base = ApplicationFramework;
        virtual bbool preInit(CommandArgs& _args) override;
        virtual void  postInit() override;
        virtual void  prepareExit() override { Base::prepareExit(); }
        virtual bbool checkForExit() override;
        virtual void overrideConfigFiles(CommandArgs& _args) override;
        virtual void platform_check(f32 _dt)  override { Base::platform_check(_dt); }
        virtual void initCheck() override { Base::initCheck(); }

        virtual void drawAll() override { Base::drawAll(); }
        virtual void drawContent() override { Base::drawContent(); }
        virtual void registerWindow(const String& _szTitleName) override { Base::registerWindow(_szTitleName); }
        virtual void mountFileDevice() override;
        virtual void registerPrefetch() override { Base::registerPrefetch(); }
        virtual void privateDestroy() override { Base::privateDestroy(); }
        virtual bbool hasToFastExit() const override { return btrue; }
        virtual void initSystemAndGraphics(const String& _szAdapter) override;

    private:

        bool initUserService();
        bool initContentPath();
        bool initModules();

        bool m_startupSuccess;
        bbool m_exitRequested;

        String8 m_rootPath;

        // auto check of check callback timings
        uint64_t m_lastCheckCBTime;
    };
}

#endif // APPLICATIONFRAMEWORK_PS5_H_
