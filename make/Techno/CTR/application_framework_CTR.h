#include "application_framework.h"

namespace ITF
{
	class ApplicationFramework_CTR : public ApplicationFramework
	{
	public:

		ApplicationFramework_CTR();
		virtual ~ApplicationFramework_CTR() {};
		
		//int init(const String& _szAdapter = "", const String& _cmdLine = "");
		
		String      getVersion()const;
		//void        update();
		//void        destroy();

	protected:
		virtual bbool preInit(CommandArgs &_args);

		void	applyExit();
		bbool	checkForExit();
		void	initSystemAndGraphics(const String& _szAdapter);
#ifndef ITF_FINAL
		void	registerPlugins(u16 _port);
		void	unregisterPlugins();
#endif // ITF_FINAL
		void	mountFileDevice();
	};
}