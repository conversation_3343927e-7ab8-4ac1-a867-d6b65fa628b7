/*
 * Copyright 1995, 2018 Perforce Software.  All rights reserved.
 *
 * This file is part of Perforce - the FAST SCM System.
 */

# define NEED_SMARTHEAP

# include <stdhdrs.h>
# include <error.h>
# include <errornum.h>
# include "msghelp.h"

# ifdef HAS_CPP11

# include "msghelp.cc"

# else

ErrorId MsgHelp::NoHelp = { ErrorOf( ES_HELP, 1, E_FAILED, EV_USAGE, 1  ), "MsgHelp::NoHelp placeholder." } ;
ErrorId MsgHelp::NoGraphHelp = { ErrorOf( ES_HELP, 235, E_FAILED, EV_USAGE, 1), "MsgHelp::NoGraphHelp placeholder." } ;
ErrorId MsgHelp::HelpPerforce = { ErrorOf( ES_HELP, 11, E_INFO, EV_NONE, 2  ), "MsgHelp::HelpPerforce placeholder." };
ErrorId MsgHelp::HelpUsage = { ErrorOf( ES_HELP, 12, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUsage placeholder." };
ErrorId MsgHelp::HelpSimple = { ErrorOf( ES_HELP, 13, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSimple"};
ErrorId MsgHelp::HelpCheckPermission = { ErrorOf( ES_HELP, 200, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCheckPermission placeholder." };
ErrorId MsgHelp::HelpCommands = { ErrorOf( ES_HELP, 14, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCommands placeholder." };
ErrorId MsgHelp::HelpUndoc = { ErrorOf( ES_HELP, 15, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpUndoc placeholder." };
ErrorId MsgHelp::HelpEnvironment = { ErrorOf( ES_HELP, 16, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpEnvironment placeholder." };
ErrorId MsgHelp::HelpFiletypes = { ErrorOf( ES_HELP, 17, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFiletypes placeholder." };
ErrorId MsgHelp::HelpJobView = { ErrorOf( ES_HELP, 18, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJobView placeholder." };
ErrorId MsgHelp::HelpRevisions = { ErrorOf( ES_HELP, 19, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRevisions placeholder." };
ErrorId MsgHelp::HelpViews = { ErrorOf( ES_HELP, 20, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpViews placeholder." };
ErrorId MsgHelp::HelpMaxResults = { ErrorOf( ES_HELP, 21, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpMaxResults placeholder." };
ErrorId MsgHelp::HelpCharset = { ErrorOf( ES_HELP, 23, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCharset placeholder." };
ErrorId MsgHelp::HelpCommandments = { ErrorOf( ES_HELP, 128, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCommandments placeholder." };
ErrorId MsgHelp::HelpCredits = { ErrorOf( ES_HELP, 24, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCredits placeholder." };
ErrorId MsgHelp::HelpAdd = { ErrorOf( ES_HELP, 25, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpAdd placeholder." };
ErrorId MsgHelp::HelpServerid = { ErrorOf( ES_HELP, 139, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpServerid placeholder." };
ErrorId MsgHelp::HelpAdmin = { ErrorOf( ES_HELP, 26, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpAdmin placeholder." };
ErrorId MsgHelp::HelpJournaldbchecksums = { ErrorOf( ES_HELP, 135, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJournaldbchecksums placeholder." };
ErrorId MsgHelp::HelpJournalcopy = { ErrorOf( ES_HELP, 171, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJournalcopy placeholder." };
ErrorId MsgHelp::HelpBranch = { ErrorOf( ES_HELP, 27, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBranch placeholder." };
ErrorId MsgHelp::HelpAnnotate = { ErrorOf( ES_HELP, 87, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpAnnotate placeholder." };
ErrorId MsgHelp::HelpArchive = { ErrorOf( ES_HELP, 125, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpArchive placeholder." };
ErrorId MsgHelp::HelpAttribute = { ErrorOf( ES_HELP, 95, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpAttribute placeholder." };
ErrorId MsgHelp::HelpBackup = { ErrorOf( ES_HELP, 190, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBackup placeholder." };
ErrorId MsgHelp::HelpBranches = { ErrorOf( ES_HELP, 28, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBranches placeholder." };
ErrorId MsgHelp::HelpBroker = { ErrorOf( ES_HELP, 149, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBroker placeholder." };
ErrorId MsgHelp::HelpChange = { ErrorOf( ES_HELP, 29, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpChange placeholder." };
ErrorId MsgHelp::HelpChanges = { ErrorOf( ES_HELP, 30, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpChanges placeholder." };
ErrorId MsgHelp::HelpClient = { ErrorOf( ES_HELP, 31, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpClient placeholder." };
ErrorId MsgHelp::HelpClients = { ErrorOf( ES_HELP, 32, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpClients placeholder." };
ErrorId MsgHelp::HelpCluster = { ErrorOf( ES_HELP, 164, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCluster placeholder." };
ErrorId MsgHelp::HelpStream = { ErrorOf( ES_HELP, 110, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStream placeholder." };
ErrorId MsgHelp::HelpStreamCmds = { ErrorOf( ES_HELP, 191, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreamCmds placeholder." };
ErrorId MsgHelp::HelpStreamintro = { ErrorOf( ES_HELP, 132, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreamintro placeholder." };
ErrorId MsgHelp::HelpStreamlog = { ErrorOf( ES_HELP, 262, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreamlog placeholder." };
ErrorId MsgHelp::HelpStreams = { ErrorOf( ES_HELP, 111, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreams placeholder." };
ErrorId MsgHelp::HelpStreamSpec = { ErrorOf( ES_HELP, 263, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreamSpec placeholder." };
ErrorId MsgHelp::HelpStreamSpecInteg = { ErrorOf( ES_HELP, 265, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpStreamSpecInteg placeholder." };
ErrorId MsgHelp::HelpBGTask = { ErrorOf( ES_HELP, 231, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBGTask placeholder." };
ErrorId MsgHelp::HelpCopy = { ErrorOf( ES_HELP, 127, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpCopy placeholder." };
ErrorId MsgHelp::HelpCounter = { ErrorOf( ES_HELP, 33, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCounter placeholder." };
ErrorId MsgHelp::HelpCounters = { ErrorOf( ES_HELP, 34, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCounters placeholder." };
ErrorId MsgHelp::HelpCstat = { ErrorOf( ES_HELP, 123, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCstat placeholder." };
ErrorId MsgHelp::HelpDepot = { ErrorOf( ES_HELP, 35, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDepot placeholder." };
ErrorId MsgHelp::HelpDepots = { ErrorOf( ES_HELP, 36, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDepots placeholder." };
ErrorId MsgHelp::HelpDiskspace = { ErrorOf( ES_HELP, 133, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDiskspace placeholder." };
ErrorId MsgHelp::HelpSizes = { ErrorOf( ES_HELP, 94,  E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSizes placeholder." };
ErrorId MsgHelp::HelpDelete = { ErrorOf( ES_HELP, 37, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDelete placeholder." };
ErrorId MsgHelp::HelpDescribe = { ErrorOf( ES_HELP, 38, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDescribe placeholder." };
ErrorId MsgHelp::HelpDiff = { ErrorOf( ES_HELP, 39, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDiff placeholder." };
ErrorId MsgHelp::HelpDiff2 = { ErrorOf( ES_HELP, 40, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDiff2 placeholder." };
ErrorId MsgHelp::HelpDirs = { ErrorOf( ES_HELP, 41, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDirs placeholder." };
ErrorId MsgHelp::HelpDuplicate = { ErrorOf( ES_HELP, 104, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDuplicate placeholder." };
ErrorId MsgHelp::HelpEdit = { ErrorOf( ES_HELP, 42, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpEdit placeholder." };
ErrorId MsgHelp::HelpExtension = { ErrorOf( ES_HELP, 251, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpExtension placeholder." };
ErrorId MsgHelp::HelpClientExtensionIntro = { ErrorOf( ES_HELP, 261, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpClientExtensionIntro placeholder." };
ErrorId MsgHelp::HelpServerExtensionIntro = { ErrorOf( ES_HELP, 253, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpServerExtensionIntro placeholder." };
ErrorId MsgHelp::HelpFailback = { ErrorOf( ES_HELP, 274, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFailback placeholder." };
ErrorId MsgHelp::HelpFailbackintro = { ErrorOf( ES_HELP, 273, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFailbackintro placeholder." };
ErrorId MsgHelp::HelpFailover = { ErrorOf( ES_HELP, 255, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFailover placeholder." };
ErrorId MsgHelp::HelpFiles = { ErrorOf( ES_HELP, 43, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFiles placeholder." };
ErrorId MsgHelp::HelpFilelog = { ErrorOf( ES_HELP, 44, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFilelog placeholder." };
ErrorId MsgHelp::HelpFix = { ErrorOf( ES_HELP, 45, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFix placeholder." };
ErrorId MsgHelp::HelpFixes = { ErrorOf( ES_HELP, 46, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFixes placeholder." };
ErrorId MsgHelp::HelpFstat = { ErrorOf( ES_HELP, 48, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFstat placeholder." };
ErrorId MsgHelp::HelpGrantPermission = { ErrorOf( ES_HELP, 197, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGrantPermission placeholder." };
ErrorId MsgHelp::HelpGrep = { ErrorOf( ES_HELP, 122, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpGrep placeholder." };
ErrorId MsgHelp::HelpGroup = { ErrorOf( ES_HELP, 49, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGroup placeholder." };
ErrorId MsgHelp::HelpGroups = { ErrorOf( ES_HELP, 50, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGroups placeholder." };
ErrorId MsgHelp::HelpHave = { ErrorOf( ES_HELP, 51, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpHave placeholder." };
ErrorId MsgHelp::HelpHeartbeat = { ErrorOf( ES_HELP, 264, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpHeartbeat placeholder." };
ErrorId MsgHelp::HelpHelp = { ErrorOf( ES_HELP, 52, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpHelp placeholder." };
ErrorId MsgHelp::HelpIgnores = { ErrorOf( ES_HELP, 189, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpIgnores placeholder." };
ErrorId MsgHelp::HelpIndex = { ErrorOf( ES_HELP, 96, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpIndex placeholder." };
ErrorId MsgHelp::HelpInfo = { ErrorOf( ES_HELP, 53, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInfo placeholder." };
ErrorId MsgHelp::HelpInteg = { ErrorOf( ES_HELP, 54, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInteg placeholder." };
ErrorId MsgHelp::HelpInteg3 = { ErrorOf( ES_HELP, 160, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInteg3 placeholder." };
ErrorId MsgHelp::HelpInteged = { ErrorOf( ES_HELP, 55, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInteged placeholder." };
ErrorId MsgHelp::HelpIstat = { ErrorOf( ES_HELP, 131, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpIstat placeholder." };
ErrorId MsgHelp::HelpJob = { ErrorOf( ES_HELP, 56, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJob placeholder." };
ErrorId MsgHelp::HelpKey = { ErrorOf( ES_HELP, 157, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpKey placeholder." };
ErrorId MsgHelp::HelpKeys = { ErrorOf( ES_HELP, 158, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpKeys placeholder." };
ErrorId MsgHelp::HelpInterchanges = { ErrorOf( ES_HELP, 97, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInterchanges placeholder." };
ErrorId MsgHelp::HelpJobs = { ErrorOf( ES_HELP, 57, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJobs placeholder." };
ErrorId MsgHelp::HelpJobSpec = { ErrorOf( ES_HELP, 58, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJobSpec placeholder." };
ErrorId MsgHelp::HelpLabel = { ErrorOf( ES_HELP, 59, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLabel placeholder." };
ErrorId MsgHelp::HelpLabels = { ErrorOf( ES_HELP, 60, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLabels placeholder." };
ErrorId MsgHelp::HelpLabelsync = { ErrorOf( ES_HELP, 61, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLabelsync placeholder." };
ErrorId MsgHelp::HelpNetworkAddress = { ErrorOf( ES_HELP, 161, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpNetworkAddress placeholder." };
ErrorId MsgHelp::HelpLdap = { ErrorOf( ES_HELP, 172, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpLdap placeholder." };
ErrorId MsgHelp::HelpLdaps = { ErrorOf( ES_HELP, 173, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLdaps placeholder." };
ErrorId MsgHelp::HelpLdapSync = { ErrorOf( ES_HELP, 187, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLdapSync placeholder." };
ErrorId MsgHelp::HelpLegal = { ErrorOf( ES_HELP, 151, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpLegal placeholder." };
ErrorId MsgHelp::HelpLicense = { ErrorOf( ES_HELP, 101, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLicense placeholder." };
ErrorId MsgHelp::HelpList = { ErrorOf( ES_HELP, 147, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpList placeholder." };
ErrorId MsgHelp::HelpLock = { ErrorOf( ES_HELP, 62, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLock placeholder." };
ErrorId MsgHelp::HelpLogger = { ErrorOf( ES_HELP, 63, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogger placeholder." };
ErrorId MsgHelp::HelpLogin = { ErrorOf( ES_HELP, 89, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogin placeholder." };
ErrorId MsgHelp::HelpLogin2 = { ErrorOf( ES_HELP, 230, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpLogin2 placeholder." };
ErrorId MsgHelp::HelpLogout = { ErrorOf( ES_HELP, 90, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpLogout placeholder." };
ErrorId MsgHelp::HelpMerge = { ErrorOf( ES_HELP, 134, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpMerge placeholder." };
ErrorId MsgHelp::HelpMerge3 = { ErrorOf( ES_HELP, 98, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpMerge3 placeholder." };
ErrorId MsgHelp::HelpMonitor = { ErrorOf( ES_HELP, 88, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpMonitor placeholder." };
ErrorId MsgHelp::HelpMove = { ErrorOf( ES_HELP, 108, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpMove placeholder." };
ErrorId MsgHelp::HelpObliterate = { ErrorOf( ES_HELP, 64, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpObliterate placeholder." };
ErrorId MsgHelp::HelpOpenableStreamSpecs = { ErrorOf( ES_HELP, 266, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpOpenableStreamSpecs placeholder." };
ErrorId MsgHelp::HelpOpened = { ErrorOf( ES_HELP, 65, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpOpened placeholder." };
ErrorId MsgHelp::HelpPasswd = { ErrorOf( ES_HELP, 66, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpPasswd placeholder." };
ErrorId MsgHelp::HelpPopulate = { ErrorOf( ES_HELP, 148, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpPopulate placeholder." };
ErrorId MsgHelp::HelpPrint = { ErrorOf( ES_HELP, 67, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpPrint placeholder." };
ErrorId MsgHelp::HelpProperty = { ErrorOf( ES_HELP, 159, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpProperty placeholder." };
ErrorId MsgHelp::HelpProtect = { ErrorOf( ES_HELP, 68, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpProtect placeholder." };
ErrorId MsgHelp::HelpProtects = { ErrorOf( ES_HELP, 93, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpProtects placeholder." };
ErrorId MsgHelp::HelpProxy = { ErrorOf( ES_HELP, 145, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpProxy placeholder." };
ErrorId MsgHelp::HelpPrune = { ErrorOf( ES_HELP, 174, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpPrune placeholder." };
ErrorId MsgHelp::HelpPubKey = { ErrorOf( ES_HELP, 201, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpPubKey placeholder." };
ErrorId MsgHelp::HelpPubKeys = { ErrorOf( ES_HELP, 202, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpPubKeys placeholder." };
ErrorId MsgHelp::HelpRealtime = { ErrorOf( ES_HELP, 268, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRealtime placeholder." };
ErrorId MsgHelp::HelpRename = { ErrorOf( ES_HELP, 69, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRename placeholder." };
ErrorId MsgHelp::HelpReopen = { ErrorOf( ES_HELP, 70, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReopen placeholder." };
ErrorId MsgHelp::HelpReconcile = { ErrorOf( ES_HELP, 146, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReconcile placeholder." };
ErrorId MsgHelp::HelpResolve = { ErrorOf( ES_HELP, 71, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpResolve placeholder." };
ErrorId MsgHelp::HelpResolved = { ErrorOf( ES_HELP, 72, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpResolved placeholder." };
ErrorId MsgHelp::HelpRestore = { ErrorOf( ES_HELP, 126, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRestore placeholder." };
ErrorId MsgHelp::HelpRetype = { ErrorOf( ES_HELP, 103, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRetype placeholder." };
ErrorId MsgHelp::HelpRevert = { ErrorOf( ES_HELP, 73, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRevert placeholder." };
ErrorId MsgHelp::HelpReview = { ErrorOf( ES_HELP, 74, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReview placeholder." };
ErrorId MsgHelp::HelpReviews = { ErrorOf( ES_HELP, 75, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReviews placeholder." };
ErrorId MsgHelp::HelpRevokePermission = { ErrorOf( ES_HELP, 198, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRevokePermission placeholder." };
ErrorId MsgHelp::HelpSearch = { ErrorOf( ES_HELP, 100, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSearch placeholder." };
ErrorId MsgHelp::HelpShowPermission = { ErrorOf( ES_HELP, 199, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpShowPermission placeholder." };
ErrorId MsgHelp::HelpShowPermissions = { ErrorOf( ES_HELP, 240, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpShowPermissions placeholder." };
ErrorId MsgHelp::HelpSnap = { ErrorOf( ES_HELP, 102, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSnap placeholder." };
ErrorId MsgHelp::HelpSet = { ErrorOf( ES_HELP, 76, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSet placeholder." };
ErrorId MsgHelp::HelpShelve = { ErrorOf( ES_HELP, 119, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpShelve placeholder." };
ErrorId MsgHelp::HelpSubmit = { ErrorOf( ES_HELP, 77, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSubmit placeholder." };
ErrorId MsgHelp::HelpSpec = { ErrorOf( ES_HELP, 99, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSpec placeholder." };
ErrorId MsgHelp::HelpSync = { ErrorOf( ES_HELP, 78, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSync placeholder." };
ErrorId MsgHelp::HelpTag = { ErrorOf( ES_HELP, 91, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTag placeholder." };
ErrorId MsgHelp::HelpTickets = { ErrorOf( ES_HELP, 92, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTickets placeholder." };
ErrorId MsgHelp::HelpTrigger = { ErrorOf( ES_HELP, 79, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTrigger placeholder." };
ErrorId MsgHelp::HelpTriggers = { ErrorOf( ES_HELP, 80, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTriggers placeholder." };
ErrorId MsgHelp::HelpTypeMap = { ErrorOf( ES_HELP, 81, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTypeMap placeholder." };
ErrorId MsgHelp::HelpUndo = { ErrorOf( ES_HELP, 203, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpUndo placeholder." };
ErrorId MsgHelp::HelpUnlock = { ErrorOf( ES_HELP, 82, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUnlock placeholder." };
ErrorId MsgHelp::HelpUnshelve = { ErrorOf( ES_HELP, 120, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUnshelve placeholder." };
ErrorId MsgHelp::HelpUser = { ErrorOf( ES_HELP, 83, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpUser placeholder." };
ErrorId MsgHelp::HelpUsers = { ErrorOf( ES_HELP, 84, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUsers placeholder." };
ErrorId MsgHelp::HelpVerify = { ErrorOf( ES_HELP, 85, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpVerify placeholder." };
ErrorId MsgHelp::HelpWhere = { ErrorOf( ES_HELP, 86, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpWhere placeholder." };
ErrorId MsgHelp::HelpTunables = { ErrorOf( ES_HELP, 106, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTunables placeholder." };
ErrorId MsgHelp::HelpDbschema = { ErrorOf( ES_HELP, 109, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDbschema placeholder." };
ErrorId MsgHelp::HelpExport = { ErrorOf( ES_HELP, 112, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpExport placeholder." };
ErrorId MsgHelp::HelpReplicate = { ErrorOf( ES_HELP, 121, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReplicate placeholder." };
ErrorId MsgHelp::HelpReshelve = { ErrorOf( ES_HELP, 193, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReshelve placeholder." };
ErrorId MsgHelp::HelpDbstat = { ErrorOf( ES_HELP, 113, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDbstat placeholder." };
ErrorId MsgHelp::HelpDbverify = { ErrorOf( ES_HELP, 141, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDbverify placeholder." };
ErrorId MsgHelp::HelpLogstat = { ErrorOf( ES_HELP, 114, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogstat placeholder." };
ErrorId MsgHelp::HelpLogappend = { ErrorOf( ES_HELP, 142, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogappend placeholder." };
ErrorId MsgHelp::HelpLogrotate = { ErrorOf( ES_HELP, 136, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogrotate placeholder." };
ErrorId MsgHelp::HelpLogparse = { ErrorOf( ES_HELP, 143, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogparse placeholder." };
ErrorId MsgHelp::HelpLogschema = { ErrorOf( ES_HELP, 144, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogschema placeholder." };
ErrorId MsgHelp::HelpLockstat = { ErrorOf( ES_HELP, 115, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLockstat placeholder." };
ErrorId MsgHelp::HelpLogtail = { ErrorOf( ES_HELP, 116, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpLogtail placeholder." };
ErrorId MsgHelp::HelpDbpack = { ErrorOf( ES_HELP, 117, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDbpack placeholder." };
ErrorId MsgHelp::HelpPing = { ErrorOf( ES_HELP, 118, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpPing placeholder." };
ErrorId MsgHelp::HelpConfigure = { ErrorOf( ES_HELP, 124, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpConfigure placeholder." };
ErrorId MsgHelp::HelpPull = { ErrorOf( ES_HELP, 129, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpPull placeholder." };
ErrorId MsgHelp::HelpConfigurables = { ErrorOf( ES_HELP, 130, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpConfigurables placeholder." };
ErrorId MsgHelp::ResolveUserHelp = { ErrorOf( ES_HELP, 22, E_INFO, EV_NONE, 1 ), "MsgHelp::ResolveUserHelp placeholder." };
ErrorId MsgHelp::HelpServer = { ErrorOf( ES_HELP, 137, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpServer placeholder." };
ErrorId MsgHelp::HelpServers = { ErrorOf( ES_HELP, 138, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpServers placeholder." };
ErrorId MsgHelp::HelpTopology = { ErrorOf( ES_HELP, 269, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTopology placeholder." };
ErrorId MsgHelp::HelpStorage = { ErrorOf( ES_HELP, 256, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpStorage placeholder." };
ErrorId MsgHelp::HelpReload = { ErrorOf( ES_HELP, 155, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReload placeholder." };
ErrorId MsgHelp::HelpUnload = { ErrorOf( ES_HELP, 156, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUnload placeholder." };
ErrorId MsgHelp::HelpAdministration = { ErrorOf( ES_HELP, 140, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpAdministration placeholder." };
ErrorId MsgHelp::HelpReplication = { ErrorOf( ES_HELP, 162, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpReplication placeholder." };
ErrorId MsgHelp::HelpBuildserver = { ErrorOf( ES_HELP, 165, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpBuildserver placeholder." };
ErrorId MsgHelp::HelpForwardingreplica = { ErrorOf( ES_HELP, 166, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpForwardingreplica placeholder." };
ErrorId MsgHelp::HelpDistributed = { ErrorOf( ES_HELP, 163, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDistributed placeholder." };
ErrorId MsgHelp::HelpCachepurge = { ErrorOf( ES_HELP, 168, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpCachepurge placeholder." };
ErrorId MsgHelp::HelpTrust = { ErrorOf( ES_HELP, 150, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpTrust placeholder." };
ErrorId MsgHelp::HelpRenameUser = { ErrorOf( ES_HELP, 169, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRenameUser placeholder." };
ErrorId MsgHelp::HelpJournals = { ErrorOf( ES_HELP, 170, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpJournals placeholder." };
ErrorId MsgHelp::HelpPush = { ErrorOf( ES_HELP, 175, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpPush placeholder." };
ErrorId MsgHelp::HelpUnzip = { ErrorOf( ES_HELP, 176, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUnzip placeholder." };
ErrorId MsgHelp::HelpUpgrades = { ErrorOf( ES_HELP, 267, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUpgrades placeholder." };
ErrorId MsgHelp::HelpZip = { ErrorOf( ES_HELP, 177, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpZip placeholder." };
ErrorId MsgHelp::HelpUnsubmit = { ErrorOf( ES_HELP, 178, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpUnsubmit placeholder." };
ErrorId MsgHelp::HelpRemote   = { ErrorOf( ES_HELP, 179, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRemote placeholder." };
ErrorId MsgHelp::HelpRemotes  = { ErrorOf( ES_HELP, 180, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRemotes placeholder." };
ErrorId MsgHelp::HelpFetch  = { ErrorOf( ES_HELP, 181, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpFetch placeholder." };
ErrorId MsgHelp::HelpDvcs  = { ErrorOf( ES_HELP, 182, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpDvcs placeholder." };
ErrorId MsgHelp::HelpSwitch  = { ErrorOf( ES_HELP, 183, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpSwitch placeholder." };
ErrorId MsgHelp::HelpInit  = { ErrorOf( ES_HELP, 184, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpInit placeholder." };
ErrorId MsgHelp::HelpClone = { ErrorOf( ES_HELP, 188, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpClone placeholder." };
ErrorId MsgHelp::HelpResubmit = { ErrorOf( ES_HELP, 185, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpResubmit placeholder." };
ErrorId MsgHelp::HelpResubmitShort = { ErrorOf( ES_HELP, 186, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpResubmitShort placeholder." };
ErrorId MsgHelp::HelpAliases = { ErrorOf( ES_HELP, 192, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpAliases placeholder." };
ErrorId MsgHelp::HelpGraph   = { ErrorOf( ES_HELP, 194, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpGraph placeholder." };
ErrorId MsgHelp::HelpGraphCommands = { ErrorOf( ES_HELP, 204, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphCommands placeholder." };
ErrorId MsgHelp::HelpGraphAdministration = { ErrorOf( ES_HELP, 205, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphAdministration placeholder." };
ErrorId MsgHelp::HelpGraphAdd = { ErrorOf( ES_HELP, 206, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphAdd placeholder." };
ErrorId MsgHelp::HelpGraphClient = { ErrorOf( ES_HELP, 207, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphClient placeholder." };
ErrorId MsgHelp::HelpGraphCherryPick = { ErrorOf( ES_HELP, 236, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphCherryPick placeholder." };
ErrorId MsgHelp::HelpGraphDelete = { ErrorOf( ES_HELP, 208, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphDelete placeholder." };
ErrorId MsgHelp::HelpGraphFiles = { ErrorOf( ES_HELP, 227, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphFiles placeholder." };
ErrorId MsgHelp::HelpGraphFstat = { ErrorOf( ES_HELP, 228, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphFstat placeholder." };
ErrorId MsgHelp::HelpGraphDescribe = { ErrorOf( ES_HELP, 209, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphDescribe placeholder." };
ErrorId MsgHelp::HelpGraphDiff = { ErrorOf( ES_HELP, 210, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphDiff placeholder." };
ErrorId MsgHelp::HelpGraphDiff2 = { ErrorOf( ES_HELP, 249, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphDiff2 placeholder." };
ErrorId MsgHelp::HelpGraphEdit = { ErrorOf( ES_HELP, 211, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphEdit placeholder." };
ErrorId MsgHelp::HelpGraphFilelog = { ErrorOf( ES_HELP, 212, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphFilelog placeholder." };
ErrorId MsgHelp::HelpGraphHave = { ErrorOf( ES_HELP, 213, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphHave placeholder." };
ErrorId MsgHelp::HelpGraphMerge = { ErrorOf( ES_HELP, 214, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpGraphMerge placeholder." };
ErrorId MsgHelp::HelpGraphUndo = { ErrorOf( ES_HELP, 252, E_INFO, EV_NONE, 0 ), "MsgHelp::HelpGraphUndo placeholder." };
ErrorId MsgHelp::HelpGraphOpened = { ErrorOf( ES_HELP, 233, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphOpened placeholder." };
ErrorId MsgHelp::HelpGraphRebase = { ErrorOf( ES_HELP, 237, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphRebase placeholder." };
ErrorId MsgHelp::HelpGraphSubmit = { ErrorOf( ES_HELP, 215, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphSubmit placeholder." };
ErrorId MsgHelp::HelpGraphSync = { ErrorOf( ES_HELP, 216, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphSync placeholder." };
ErrorId MsgHelp::HelpGraphTag = { ErrorOf( ES_HELP, 217, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphTag placeholder." };
ErrorId MsgHelp::HelpGraphTags = { ErrorOf( ES_HELP, 242, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphTags placeholder." };
ErrorId MsgHelp::HelpGraphSwitch  = { ErrorOf( ES_HELP, 218, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphSwitch placeholder." };
ErrorId MsgHelp::HelpGraphReconcile = { ErrorOf( ES_HELP, 226, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphReconcile placeholder." };
ErrorId MsgHelp::HelpGraphRevert = { ErrorOf( ES_HELP, 234, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphRevert placeholder." };
ErrorId MsgHelp::HelpRepo   = { ErrorOf( ES_HELP, 195, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRepo placeholder." };
ErrorId MsgHelp::HelpRepos  = { ErrorOf( ES_HELP, 196, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpRepos placeholder." };
ErrorId MsgHelp::HelpGraphReceivePack = { ErrorOf( ES_HELP, 219, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphReceivePack placeholder." };
ErrorId MsgHelp::HelpGraphShowRef = { ErrorOf( ES_HELP, 220, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphShowRef placeholder." };
ErrorId MsgHelp::HelpGraphRefHist = { ErrorOf( ES_HELP, 221, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphRefHist placeholder." };
ErrorId MsgHelp::HelpGraphPackObjects = { ErrorOf( ES_HELP, 222, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphPackObjects placeholder." };
ErrorId MsgHelp::HelpGraphSubmodule = { ErrorOf( ES_HELP, 224, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphSubmodule placeholder." };
ErrorId MsgHelp::HelpGraphLfsLocks = { ErrorOf( ES_HELP, 257, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsLocks placeholder." };
ErrorId MsgHelp::HelpGraphLfsLock = { ErrorOf( ES_HELP, 258, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsLock placeholder." };
ErrorId MsgHelp::HelpGraphLfsUnLock = { ErrorOf( ES_HELP, 259, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsUnLock placeholder." };
ErrorId MsgHelp::HelpGraphLock = { ErrorOf( ES_HELP, 223, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLock placeholder." };
ErrorId MsgHelp::HelpGraphResolve = { ErrorOf( ES_HELP, 244, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphResolve placeholder." };
ErrorId MsgHelp::HelpGraphRevList = { ErrorOf( ES_HELP, 243, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphRevList placeholder." };
ErrorId MsgHelp::HelpGraphLfsPush = { ErrorOf( ES_HELP, 245, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsPush placeholder." };
ErrorId MsgHelp::HelpGraphLfsFetch = { ErrorOf( ES_HELP, 246, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsFetch placeholder." };
ErrorId MsgHelp::HelpGraphLfsStat = { ErrorOf( ES_HELP, 247, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLfsStat placeholder." };
ErrorId MsgHelp::HelpGraphVerify = { ErrorOf( ES_HELP, 248, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphVerify placeholder." };
ErrorId MsgHelp::HelpGraphLog = { ErrorOf( ES_HELP, 225, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLog placeholder." };
ErrorId MsgHelp::HelpGraphLsTree = { ErrorOf( ES_HELP, 229, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphLsTree placeholder." };
ErrorId MsgHelp::HelpGraphUnlock = { ErrorOf( ES_HELP, 238, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphUnlock placeholder." };
ErrorId MsgHelp::HelpGraphPrint = { ErrorOf( ES_HELP, 239, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphPrint placeholder." };
ErrorId MsgHelp::HelpGraphDirs = { ErrorOf( ES_HELP, 241, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphDirs placeholder." };
ErrorId MsgHelp::HelpGraphCatFile = { ErrorOf( ES_HELP, 250, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphCatFile placeholder." };
ErrorId MsgHelp::HelpGraphRecomputeRefcnts = { ErrorOf( ES_HELP, 270, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphRecomputeRefcnts placeholder." };
ErrorId MsgHelp::HelpGraphGc = { ErrorOf( ES_HELP, 271, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphGc placeholder." };
ErrorId MsgHelp::HelpGraphPurgeRefhist = { ErrorOf( ES_HELP, 272, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpGraphPurgeRefhist placeholder." };
ErrorId MsgHelp::Help2FA = { ErrorOf( ES_HELP, 232, E_INFO, EV_NONE, 0  ), "MsgHelp::Help2FA placeholder." };

// ErrorId graveyard: retired/deprecated ErrorIds.

ErrorId MsgHelp::HelpBrowse = { ErrorOf( ES_HELP, 107, E_INFO, EV_NONE, 0  ), "graveyard: retired placeholder." }; // DEPRECATED 2013.1 removed ZeroConf
ErrorId MsgHelp::HelpZeroConf = { ErrorOf( ES_HELP, 105, E_INFO, EV_NONE, 0  ), "MsgHelp::HelpZeroConf placeholder." }; // DEPRECATED 2013.1 removed ZeroConf

# endif
