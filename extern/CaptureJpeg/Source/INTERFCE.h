//-----------------------------------------------------------------------------
// ---------------------
// File ....: interfce.h
// ---------------------
// Author...: <PERSON>
// Date ....: October 1995
// Descr....: C Interface to JPEG library
//
// History .: Oct, 27 1995 - Started
//
//-----------------------------------------------------------------------------

typedef struct _jpegdata {
   unsigned char *ptr;
   int    width;
   int    height;
   void	  *output_file;
   int    aritcoding;
   int    CCIR601sampling;
   int    smoothingfactor;
   int    quality;
   void	  *hWnd;
   int    status;
   int    components;
} JPEGDATA;
#ifdef PSX2_TARGET
#pragma pack(0)
#endif

// 		ucJPEG_INPOUT_FILE is the address of the file buffer.
// 		lJPEG_INPOUT_FILE_LENGHT is the lenght of the file.
// 		you have an example in texfile & in texframe.

extern  __declspec(thread) unsigned char *ucJPEG_INPOUT_FILE;
extern  __declspec(thread) size_t lJPEG_INPOUT_FILE_LENGHT;

void JpegWrite( JPEGDATA *data );
void JpegInfo(  JPEGDATA *data );
void JpegRead(  JPEGDATA *data );


extern void Invert24YAndRB(unsigned char *dst, unsigned char *src, unsigned int w, unsigned int h);

//-- interfce.c ---------------------------------------------------------------
