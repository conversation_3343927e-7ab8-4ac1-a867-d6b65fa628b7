
/*
typedef unsigned char uint8;

static int Mul8Bit(int a, int b)
{
	int t = a * b + 128;
	return (t + (t >> 8)) >> 8;
}

static inline int Lerp13(int fm, int to)
{
	return (fm * 2 + to) / 3;
}

static void PrepareOptTable(uint8 * Table, const uint8 * expand, int size)
{
	for (int i = 0; i < 256; i++)
	{
		float bestErr = 256;

		for (int min = 0; min < size; min++)
		{
			for (int max = 0; max < size; max++)
			{
				int mine = expand[min];
				int maxe = expand[max];
				float err = abs(maxe + Mul8Bit(mine-maxe, 0x55) - i);
				err += 0.03f * abs(max - min);

				if (err < bestErr)
				{
					Table[i*2+0] = max;
					Table[i*2+1] = min;
					bestErr = err;
				}
			}
		}
	}
}


void initTables()
{
	uint8 Expand5[32];
	uint8 Expand6[64];

	for(sInt i=0;i<32;i++)
		Expand5[i] = (i<<3)|(i>>2);

	for(sInt i=0;i<64;i++)
		Expand6[i] = (i<<2)|(i>>4);

	PrepareOptTable(OMatch5, Expand5, 32)
	PrepareOptTable(OMatch6, Expand6, 64)
};
*/

#if __CUDACC__
__constant__ unsigned short
#else
const static uint8 
#endif
OMatch5[256][2] =
{
	{0x00, 0x00},
	{0x00, 0x00},
	{0x00, 0x01},
	{0x00, 0x01},
	{0x01, 0x00},
	{0x01, 0x00},
	{0x01, 0x00},
	{0x01, 0x01},
	{0x01, 0x01},
	{0x01, 0x01},
	{0x01, 0x02},
	{0x00, 0x04},
	{0x02, 0x01},
	{0x02, 0x01},
	{0x02, 0x01},
	{0x02, 0x02},
	{0x02, 0x02},
	{0x02, 0x02},
	{0x02, 0x03},
	{0x01, 0x05},
	{0x03, 0x02},
	{0x03, 0x02},
	{0x04, 0x00},
	{0x03, 0x03},
	{0x03, 0x03},
	{0x03, 0x03},
	{0x03, 0x04},
	{0x03, 0x04},
	{0x03, 0x04},
	{0x03, 0x05},
	{0x04, 0x03},
	{0x04, 0x03},
	{0x05, 0x02},
	{0x04, 0x04},
	{0x04, 0x04},
	{0x04, 0x05},
	{0x04, 0x05},
	{0x05, 0x04},
	{0x05, 0x04},
	{0x05, 0x04},
	{0x06, 0x03},
	{0x05, 0x05},
	{0x05, 0x05},
	{0x05, 0x06},
	{0x04, 0x08},
	{0x06, 0x05},
	{0x06, 0x05},
	{0x06, 0x05},
	{0x06, 0x06},
	{0x06, 0x06},
	{0x06, 0x06},
	{0x06, 0x07},
	{0x05, 0x09},
	{0x07, 0x06},
	{0x07, 0x06},
	{0x08, 0x04},
	{0x07, 0x07},
	{0x07, 0x07},
	{0x07, 0x07},
	{0x07, 0x08},
	{0x07, 0x08},
	{0x07, 0x08},
	{0x07, 0x09},
	{0x08, 0x07},
	{0x08, 0x07},
	{0x09, 0x06},
	{0x08, 0x08},
	{0x08, 0x08},
	{0x08, 0x09},
	{0x08, 0x09},
	{0x09, 0x08},
	{0x09, 0x08},
	{0x09, 0x08},
	{0x0A, 0x07},
	{0x09, 0x09},
	{0x09, 0x09},
	{0x09, 0x0A},
	{0x08, 0x0C},
	{0x0A, 0x09},
	{0x0A, 0x09},
	{0x0A, 0x09},
	{0x0A, 0x0A},
	{0x0A, 0x0A},
	{0x0A, 0x0A},
	{0x0A, 0x0B},
	{0x09, 0x0D},
	{0x0B, 0x0A},
	{0x0B, 0x0A},
	{0x0C, 0x08},
	{0x0B, 0x0B},
	{0x0B, 0x0B},
	{0x0B, 0x0B},
	{0x0B, 0x0C},
	{0x0B, 0x0C},
	{0x0B, 0x0C},
	{0x0B, 0x0D},
	{0x0C, 0x0B},
	{0x0C, 0x0B},
	{0x0D, 0x0A},
	{0x0C, 0x0C},
	{0x0C, 0x0C},
	{0x0C, 0x0D},
	{0x0C, 0x0D},
	{0x0D, 0x0C},
	{0x0D, 0x0C},
	{0x0D, 0x0C},
	{0x0E, 0x0B},
	{0x0D, 0x0D},
	{0x0D, 0x0D},
	{0x0D, 0x0E},
	{0x0C, 0x10},
	{0x0E, 0x0D},
	{0x0E, 0x0D},
	{0x0E, 0x0D},
	{0x0E, 0x0E},
	{0x0E, 0x0E},
	{0x0E, 0x0E},
	{0x0E, 0x0F},
	{0x0D, 0x11},
	{0x0F, 0x0E},
	{0x0F, 0x0E},
	{0x10, 0x0C},
	{0x0F, 0x0F},
	{0x0F, 0x0F},
	{0x0F, 0x0F},
	{0x0F, 0x10},
	{0x0F, 0x10},
	{0x0F, 0x10},
	{0x0F, 0x11},
	{0x10, 0x0F},
	{0x10, 0x0F},
	{0x11, 0x0E},
	{0x10, 0x10},
	{0x10, 0x10},
	{0x10, 0x11},
	{0x10, 0x11},
	{0x11, 0x10},
	{0x11, 0x10},
	{0x11, 0x10},
	{0x12, 0x0F},
	{0x11, 0x11},
	{0x11, 0x11},
	{0x11, 0x12},
	{0x10, 0x14},
	{0x12, 0x11},
	{0x12, 0x11},
	{0x12, 0x11},
	{0x12, 0x12},
	{0x12, 0x12},
	{0x12, 0x12},
	{0x12, 0x13},
	{0x11, 0x15},
	{0x13, 0x12},
	{0x13, 0x12},
	{0x14, 0x10},
	{0x13, 0x13},
	{0x13, 0x13},
	{0x13, 0x13},
	{0x13, 0x14},
	{0x13, 0x14},
	{0x13, 0x14},
	{0x13, 0x15},
	{0x14, 0x13},
	{0x14, 0x13},
	{0x15, 0x12},
	{0x14, 0x14},
	{0x14, 0x14},
	{0x14, 0x15},
	{0x14, 0x15},
	{0x15, 0x14},
	{0x15, 0x14},
	{0x15, 0x14},
	{0x16, 0x13},
	{0x15, 0x15},
	{0x15, 0x15},
	{0x15, 0x16},
	{0x14, 0x18},
	{0x16, 0x15},
	{0x16, 0x15},
	{0x16, 0x15},
	{0x16, 0x16},
	{0x16, 0x16},
	{0x16, 0x16},
	{0x16, 0x17},
	{0x15, 0x19},
	{0x17, 0x16},
	{0x17, 0x16},
	{0x18, 0x14},
	{0x17, 0x17},
	{0x17, 0x17},
	{0x17, 0x17},
	{0x17, 0x18},
	{0x17, 0x18},
	{0x17, 0x18},
	{0x17, 0x19},
	{0x18, 0x17},
	{0x18, 0x17},
	{0x19, 0x16},
	{0x18, 0x18},
	{0x18, 0x18},
	{0x18, 0x19},
	{0x18, 0x19},
	{0x19, 0x18},
	{0x19, 0x18},
	{0x19, 0x18},
	{0x1A, 0x17},
	{0x19, 0x19},
	{0x19, 0x19},
	{0x19, 0x1A},
	{0x18, 0x1C},
	{0x1A, 0x19},
	{0x1A, 0x19},
	{0x1A, 0x19},
	{0x1A, 0x1A},
	{0x1A, 0x1A},
	{0x1A, 0x1A},
	{0x1A, 0x1B},
	{0x19, 0x1D},
	{0x1B, 0x1A},
	{0x1B, 0x1A},
	{0x1C, 0x18},
	{0x1B, 0x1B},
	{0x1B, 0x1B},
	{0x1B, 0x1B},
	{0x1B, 0x1C},
	{0x1B, 0x1C},
	{0x1B, 0x1C},
	{0x1B, 0x1D},
	{0x1C, 0x1B},
	{0x1C, 0x1B},
	{0x1D, 0x1A},
	{0x1C, 0x1C},
	{0x1C, 0x1C},
	{0x1C, 0x1D},
	{0x1C, 0x1D},
	{0x1D, 0x1C},
	{0x1D, 0x1C},
	{0x1D, 0x1C},
	{0x1E, 0x1B},
	{0x1D, 0x1D},
	{0x1D, 0x1D},
	{0x1D, 0x1E},
	{0x1D, 0x1E},
	{0x1E, 0x1D},
	{0x1E, 0x1D},
	{0x1E, 0x1D},
	{0x1E, 0x1E},
	{0x1E, 0x1E},
	{0x1E, 0x1E},
	{0x1E, 0x1F},
	{0x1E, 0x1F},
	{0x1F, 0x1E},
	{0x1F, 0x1E},
	{0x1F, 0x1E},
	{0x1F, 0x1F},
	{0x1F, 0x1F},
};

#if __CUDACC__
__constant__ unsigned short
#else
const static uint8
#endif
OMatch6[256][2] =
{
	{0x00, 0x00},
	{0x00, 0x01},
	{0x01, 0x00},
	{0x01, 0x01},
	{0x01, 0x01},
	{0x01, 0x02},
	{0x02, 0x01},
	{0x02, 0x02},
	{0x02, 0x02},
	{0x02, 0x03},
	{0x03, 0x02},
	{0x03, 0x03},
	{0x03, 0x03},
	{0x03, 0x04},
	{0x04, 0x03},
	{0x04, 0x04},
	{0x04, 0x04},
	{0x04, 0x05},
	{0x05, 0x04},
	{0x05, 0x05},
	{0x05, 0x05},
	{0x05, 0x06},
	{0x06, 0x05},
	{0x00, 0x11},
	{0x06, 0x06},
	{0x06, 0x07},
	{0x07, 0x06},
	{0x02, 0x10},
	{0x07, 0x07},
	{0x07, 0x08},
	{0x08, 0x07},
	{0x03, 0x11},
	{0x08, 0x08},
	{0x08, 0x09},
	{0x09, 0x08},
	{0x05, 0x10},
	{0x09, 0x09},
	{0x09, 0x0A},
	{0x0A, 0x09},
	{0x06, 0x11},
	{0x0A, 0x0A},
	{0x0A, 0x0B},
	{0x0B, 0x0A},
	{0x08, 0x10},
	{0x0B, 0x0B},
	{0x0B, 0x0C},
	{0x0C, 0x0B},
	{0x09, 0x11},
	{0x0C, 0x0C},
	{0x0C, 0x0D},
	{0x0D, 0x0C},
	{0x0B, 0x10},
	{0x0D, 0x0D},
	{0x0D, 0x0E},
	{0x0E, 0x0D},
	{0x0C, 0x11},
	{0x0E, 0x0E},
	{0x0E, 0x0F},
	{0x0F, 0x0E},
	{0x0E, 0x10},
	{0x0F, 0x0F},
	{0x0F, 0x10},
	{0x10, 0x0E},
	{0x10, 0x0F},
	{0x11, 0x0E},
	{0x10, 0x10},
	{0x10, 0x11},
	{0x11, 0x10},
	{0x12, 0x0F},
	{0x11, 0x11},
	{0x11, 0x12},
	{0x12, 0x11},
	{0x14, 0x0E},
	{0x12, 0x12},
	{0x12, 0x13},
	{0x13, 0x12},
	{0x15, 0x0F},
	{0x13, 0x13},
	{0x13, 0x14},
	{0x14, 0x13},
	{0x17, 0x0E},
	{0x14, 0x14},
	{0x14, 0x15},
	{0x15, 0x14},
	{0x18, 0x0F},
	{0x15, 0x15},
	{0x15, 0x16},
	{0x16, 0x15},
	{0x1A, 0x0E},
	{0x16, 0x16},
	{0x16, 0x17},
	{0x17, 0x16},
	{0x1B, 0x0F},
	{0x17, 0x17},
	{0x17, 0x18},
	{0x18, 0x17},
	{0x13, 0x21},
	{0x18, 0x18},
	{0x18, 0x19},
	{0x19, 0x18},
	{0x15, 0x20},
	{0x19, 0x19},
	{0x19, 0x1A},
	{0x1A, 0x19},
	{0x16, 0x21},
	{0x1A, 0x1A},
	{0x1A, 0x1B},
	{0x1B, 0x1A},
	{0x18, 0x20},
	{0x1B, 0x1B},
	{0x1B, 0x1C},
	{0x1C, 0x1B},
	{0x19, 0x21},
	{0x1C, 0x1C},
	{0x1C, 0x1D},
	{0x1D, 0x1C},
	{0x1B, 0x20},
	{0x1D, 0x1D},
	{0x1D, 0x1E},
	{0x1E, 0x1D},
	{0x1C, 0x21},
	{0x1E, 0x1E},
	{0x1E, 0x1F},
	{0x1F, 0x1E},
	{0x1E, 0x20},
	{0x1F, 0x1F},
	{0x1F, 0x20},
	{0x20, 0x1E},
	{0x20, 0x1F},
	{0x21, 0x1E},
	{0x20, 0x20},
	{0x20, 0x21},
	{0x21, 0x20},
	{0x22, 0x1F},
	{0x21, 0x21},
	{0x21, 0x22},
	{0x22, 0x21},
	{0x24, 0x1E},
	{0x22, 0x22},
	{0x22, 0x23},
	{0x23, 0x22},
	{0x25, 0x1F},
	{0x23, 0x23},
	{0x23, 0x24},
	{0x24, 0x23},
	{0x27, 0x1E},
	{0x24, 0x24},
	{0x24, 0x25},
	{0x25, 0x24},
	{0x28, 0x1F},
	{0x25, 0x25},
	{0x25, 0x26},
	{0x26, 0x25},
	{0x2A, 0x1E},
	{0x26, 0x26},
	{0x26, 0x27},
	{0x27, 0x26},
	{0x2B, 0x1F},
	{0x27, 0x27},
	{0x27, 0x28},
	{0x28, 0x27},
	{0x23, 0x31},
	{0x28, 0x28},
	{0x28, 0x29},
	{0x29, 0x28},
	{0x25, 0x30},
	{0x29, 0x29},
	{0x29, 0x2A},
	{0x2A, 0x29},
	{0x26, 0x31},
	{0x2A, 0x2A},
	{0x2A, 0x2B},
	{0x2B, 0x2A},
	{0x28, 0x30},
	{0x2B, 0x2B},
	{0x2B, 0x2C},
	{0x2C, 0x2B},
	{0x29, 0x31},
	{0x2C, 0x2C},
	{0x2C, 0x2D},
	{0x2D, 0x2C},
	{0x2B, 0x30},
	{0x2D, 0x2D},
	{0x2D, 0x2E},
	{0x2E, 0x2D},
	{0x2C, 0x31},
	{0x2E, 0x2E},
	{0x2E, 0x2F},
	{0x2F, 0x2E},
	{0x2E, 0x30},
	{0x2F, 0x2F},
	{0x2F, 0x30},
	{0x30, 0x2E},
	{0x30, 0x2F},
	{0x31, 0x2E},
	{0x30, 0x30},
	{0x30, 0x31},
	{0x31, 0x30},
	{0x32, 0x2F},
	{0x31, 0x31},
	{0x31, 0x32},
	{0x32, 0x31},
	{0x34, 0x2E},
	{0x32, 0x32},
	{0x32, 0x33},
	{0x33, 0x32},
	{0x35, 0x2F},
	{0x33, 0x33},
	{0x33, 0x34},
	{0x34, 0x33},
	{0x37, 0x2E},
	{0x34, 0x34},
	{0x34, 0x35},
	{0x35, 0x34},
	{0x38, 0x2F},
	{0x35, 0x35},
	{0x35, 0x36},
	{0x36, 0x35},
	{0x3A, 0x2E},
	{0x36, 0x36},
	{0x36, 0x37},
	{0x37, 0x36},
	{0x3B, 0x2F},
	{0x37, 0x37},
	{0x37, 0x38},
	{0x38, 0x37},
	{0x3D, 0x2E},
	{0x38, 0x38},
	{0x38, 0x39},
	{0x39, 0x38},
	{0x3E, 0x2F},
	{0x39, 0x39},
	{0x39, 0x3A},
	{0x3A, 0x39},
	{0x3A, 0x3A},
	{0x3A, 0x3A},
	{0x3A, 0x3B},
	{0x3B, 0x3A},
	{0x3B, 0x3B},
	{0x3B, 0x3B},
	{0x3B, 0x3C},
	{0x3C, 0x3B},
	{0x3C, 0x3C},
	{0x3C, 0x3C},
	{0x3C, 0x3D},
	{0x3D, 0x3C},
	{0x3D, 0x3D},
	{0x3D, 0x3D},
	{0x3D, 0x3E},
	{0x3E, 0x3D},
	{0x3E, 0x3E},
	{0x3E, 0x3E},
	{0x3E, 0x3F},
	{0x3F, 0x3E},
	{0x3F, 0x3F},
	{0x3F, 0x3F},
};

