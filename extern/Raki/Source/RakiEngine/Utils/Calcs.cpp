#include "Precompiled.h"







#include "RakiEngine/Utils/Calcs.h"






namespace raki
{



const f32 Calcs::getLinearVolumeFromDecibelVolume( f32 _decibelVolume )
{
    static const f32 log10dividedBy20 = 0.1151292546497f;

    return expf( _decibelVolume * log10dividedBy20 ) ; 
}


const f32 Calcs::getDecibelVolumeFromLinearVolume( f32 _linearVolume )
{
    static const f32 log10dividedBy20 = 0.1151292546497f;

    return logf( _linearVolume ) / log10dividedBy20;
}


const f32 Calcs::getFrequencyFromMidiNote( u8 _midiNote )
{
    // C-2, MIDI note 0, is 8,175798916 Hz

    return 8.175798916f * getPitchFromInterval( (i32) _midiNote );
}


const f32 Calcs::getPitchFromInterval( i32 _interval )
{
    static const f32 log12thRoot2 = 0.0577622650466f; // (f32)log( 1.0594630943592952645618252949463 );

    return (f32) exp( log12thRoot2 * (float)_interval );
}






} // namespace raki



