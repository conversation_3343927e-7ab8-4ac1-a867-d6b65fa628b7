#ifndef RAKI_TESTINITIALIZER_H
#define RAKI_TESTINITIALIZER_H


#include "RakiEngine/System/Memory/SystemAllocator.h"

#if defined ( RAKI_PLATFORM_PS3 )
#include "RakiEngine/Specific/_PS3/Specific_PS3.h"
#endif // ( RAKI_PLATFORM_PS3 )


// test initializer: this should only be used in tests 

namespace raki
{
    class TestInitializer
    {
    public:
        TestInitializer();
        ~TestInitializer();

    private:
        SystemAllocator m_allocator;

#if defined ( RAKI_USING_XAUDIO2 )
        IXAudio2 * m_xaudio2; 
        IXAudio2MasteringVoice * m_masteringVoice;
#endif // ( RAKI_USING_XAUDIO2 )


#if defined ( RAKI_PLATFORM_PS3 )

        bool init();
        bool uninit();

        bool loadModules();
        bool unloadModules();

        u32 m_audioPortNumber;
        void * m_multistreamMemory;
        void * m_mp3Memory;

        MultiStreamUpdateThread * m_multiStreamUpdateThread;

#endif // ( RAKI_USING_XAUDIO2 )


#if defined ( RAKI_PLATFORM_WII )
        void * m_axBuffer;
#endif // ( RAKI_PLATFORM_WII )

    };


} // namespace raki


#endif // RAKI_TESTINITIALIZER_H

