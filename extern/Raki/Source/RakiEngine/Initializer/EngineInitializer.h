#ifndef RAKI_ENGINEINITIALIZER_H
#define RAKI_ENGINEINITIALIZER_H



// internal engine initializer



namespace raki
{


class Allocator;
class Initializer;
class TestInitializer;


class EngineInitializer
{
public:

    struct InitStruct
    {
#if defined ( RAKI_USING_XAUDIO2 )
        IXAudio2 * m_xaudio2;
#elif defined ( RAKI_PLATFORM_PS3 )
        void * m_ps3;
#elif defined ( RAKI_PLATFORM_WII )
        bool m_consoleIsSetAsMono;
        void * m_axBuffer;
#endif // 
    };

    friend class Initializer;
    friend class TestInitializer;


    RAKI_DECLARE_SINGLETON(EngineInitializer);

protected:
    EngineInitializer();

public:
    ~EngineInitializer();

    void initialize( InitStruct * _initStruct );
    void uninitialize();

    void setStreamBufferSizeDuration( const f32 _streamBufferSizeDuration );
    const u32 getStreamBufferSize( const u32 _format, const u32 _nbChannels );

#if defined ( RAKI_USING_XAUDIO2 )
    inline IXAudio2 * getXAudio2();
    inline const u32 getDeviceChannelMask();
    inline const u32 getDeviceNbChannels();
#endif // 

#if defined ( RAKI_PLATFORM_WII )

    inline bool getConsoleIsSetAsMono();

#endif // 

protected:

#if defined ( RAKI_USING_XAUDIO2 )
    IXAudio2 * m_xaudio2;
    u32 m_deviceChannelMask;
    u32 m_deviceNbChannels;
#endif // 

private:

    f32 m_streamBufferSizeDuration;

#if defined ( RAKI_PLATFORM_WII )

    bool m_consoleIsSetAsMono;

#endif // 
};



#if defined ( RAKI_USING_XAUDIO2 )

IXAudio2 * EngineInitializer::getXAudio2()
{
    RAKI_ASSERT( m_xaudio2 );

    return m_xaudio2;
}

const u32 EngineInitializer::getDeviceChannelMask()
{
    return m_deviceChannelMask;
}

const u32 EngineInitializer::getDeviceNbChannels()
{
    return m_deviceNbChannels;
}

#elif defined ( RAKI_PLATFORM_WII )

bool EngineInitializer::getConsoleIsSetAsMono()
{
    return m_consoleIsSetAsMono;
}



#endif // 

} // namespace raki


#endif // RAKI_ENGINEINITIALIZER_H

