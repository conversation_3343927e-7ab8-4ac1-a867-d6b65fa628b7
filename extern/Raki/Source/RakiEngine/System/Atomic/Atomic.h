#ifndef RAKI_ATOMIC_H
#define RAKI_ATOMIC_H






namespace raki
{

    class Atomic
    {

    public:

        static void setWithMemBarrier( volatile i32 & _ref, const i32 _value );
        static u32 getWithMemBarrier( const volatile i32& _ref );

        static void setWithMemBarrier( volatile u32 & _ref, const u32 _value );
        static u32 getWithMemBarrier( const volatile u32& _ref );

        static void setWithMemBarrier( volatile bool & _ref, const bool _value );
        static bool getWithMemBarrier( const volatile bool& _ref );

        template <typename T>
        static void setWithMemBarrier( volatile T * & _ref, const T * _value )
        {
            _ref = (volatile T * )_value;

            // volatiles are real volatiles on win32: no need for memory barriers on win32

#if defined WORK_PLATFORM_XB360
            __lwsync();
#endif // WORK_PLATFORM_XB360
        }


        template <typename T>
        static T * getWithMemBarrier( const T volatile * & _ref )
        {
            // volatiles are real volatiles on win32: no need for memory barriers on win32

#if defined WORK_PLATFORM_XB360
            __lwsync();
#endif // WORK_PLATFORM_XB360

            return _ref;
        }


        template <typename T>
        static T * getWithMemBarrier( T volatile * & _ref )
        {
            // volatiles are real volatiles on win32: no need for memory barriers on win32

#if defined WORK_PLATFORM_XB360
            __lwsync();
#endif // WORK_PLATFORM_XB360

            T * value = ( T * ) _ref;

            return value;
        }

};



} // namespace raki

#endif // RAKI_ATOMIC_H

