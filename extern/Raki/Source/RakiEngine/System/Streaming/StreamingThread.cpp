#include "Precompiled.h"

#include "RakiEngine/System/Streaming/StreamingThread.h"

#include "RakiEngine/Data/Stream/SeekableStream.h"
//#define LOG_STREAMINGTHREAD

namespace raki 
{



StreamingThread::StreamingThread()
    : m_requestCommandList( STREAMINGTHREAD_COMMANDLIST_SIZE )
    , m_stopThreadRequested( false )
{
    u32 ui;

    for ( ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
    {
        m_streamingRequestArray[ui].reinit();
        m_processingSlots[ui] = NULL;
    }
}


StreamingThread::~StreamingThread()
{
    // check that all requests have been closed

    for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
    {
        RAKI_ASSERT( m_processingSlots[ui] == NULL );
    }
}

void StreamingThread::start()
{
    m_stopThreadRequested = false;

    startThread();
}


void StreamingThread::stop()
{
    m_stopThreadRequested = true;
}


void StreamingThread::run()
{
    RAKI_OUTPUT("StreamingThread::run starting thread update");

    u32 currentIndex = 0;

    while ( !m_stopThreadRequested )
    {
        while ( !canProcessRequest() )
            Thread::sleep( 15 );

        // first: check if requests are pending 

        Request * request = m_requestCommandList.getElementIfAvailable();

        while ( request )
        {
            switch ( request->m_type )
            {
            case read:
                {
                    RAKI_ASSERT( request->m_streamingRequest->getState() == StreamingRequest::readRequested );

                    // find empty slot

                    bool done = false;

                    for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
                    {
                        u32 index = ( currentIndex + ui ) % STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE; 

                        if ( !m_processingSlots[index] )
                        {
                            m_processingSlots[index] = request->m_streamingRequest;

                            done = true;

                            break;
                        }
                    }

                    RAKI_ASSERT( done );
                }
                break;


            case cancel:
                {
                    for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
                    {
                        if ( m_processingSlots[ui] == request->m_streamingRequest )
                        {
                            m_processingSlots[ui]->setState( StreamingRequest::readCancelled );

                            m_processingSlots[ui] = NULL;
                        }
                    }
                }
                break;


            case close:
                {
                    for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
                    {
                        if ( m_processingSlots[ui] == request->m_streamingRequest )
                        {
                            RAKI_ASSERT( m_processingSlots[ui]->getSizeRead() != StreamingRequest::readRequested );
                            m_processingSlots[ui]->reinit();
                            m_processingSlots[ui] = NULL;
                        }
                    }
                }
                break;


            default:
                {
                    RAKI_ASSERT_FALSE;
                }

            }

            request = m_requestCommandList.getElementIfAvailable();
        }

        // now: process one request

        for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
        {
            u32 index = ( currentIndex + ui ) % STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE; 

            StreamingRequest * processedRequest = m_processingSlots[index];

            if ( processedRequest  && ( processedRequest->getState() == StreamingRequest::readRequested ) )
            {
                // seek if necessary 
                if ( processedRequest->m_stream->getCurrentPos() != processedRequest->getPosition() )
                {
                    processedRequest->m_stream->seek( processedRequest->getPosition(), SeekableStream::fromBeginning );
                }

                u64 sizeRead = processedRequest->m_stream->stream( processedRequest->getBuffer(), processedRequest->getSizeRequested() );

                processedRequest->setSizeRead( sizeRead );

                processedRequest->setState( StreamingRequest::readFinished );

                ++currentIndex;

                break;
            }
        }

        // sleep
        Thread::sleep( 15 );

    }

    RAKI_OUTPUT("StreamingThread::run ending thread update");

}


StreamingRequest * StreamingThread::addReadRequest( SeekableStream * _stream, const u64 _position, const u64 _sizeRequested, void * _buffer )
{
    RAKI_ASSERT( _stream );
    RAKI_ASSERT( _sizeRequested );
    RAKI_ASSERT( _buffer );

    // first find a free StreamingRequest

    for ( u32 ui = 0 ; ui < STREAMINGTHREAD_STREAMINGREQUESTARRAY_SIZE ; ++ui )
    {
        if ( m_streamingRequestArray[ui].getState() == StreamingRequest::notUsed )
        {
            m_streamingRequestArray[ui].setReadRequest( _stream, _position, _sizeRequested, _buffer );

            m_streamingRequestArray[ui].setState( StreamingRequest::readRequested );

            Request request( read, &m_streamingRequestArray[ui] );
            m_requestCommandList.addElement( request );

#ifdef LOG_STREAMINGTHREAD
            RAKI_OUTPUT("0x%x 0x%x StreamingThread::addReadRequest _stream 0x%x _position 0x%x _sizeRequested 0x%x = %d _buffer 0x%x returning request 0x%x", 
                _buffer, this, _stream, _position, _sizeRequested, _sizeRequested, (u32)_buffer, &m_streamingRequestArray[ui] );
#endif // LOG_STREAMINGTHREAD

            return &m_streamingRequestArray[ui];
        }
    }

#ifdef LOG_STREAMINGTHREAD
    RAKI_OUTPUT("0x%x StreamingThread::addReadRequest _stream 0x%x _position 0x%x _sizeRequested 0x%x = %d _buffer 0x%x returning NULL request ", 
        this, _stream, _position, _sizeRequested, _sizeRequested, (u32)_buffer );
#endif // LOG_STREAMINGTHREAD

    return NULL;
}


void StreamingThread::cancelReadRequest( StreamingRequest * const _request ) 
{
#ifdef LOG_STREAMINGTHREAD
    RAKI_OUTPUT("0x%x StreamingThread::cancelReadRequest _stream 0x%x ", this, _request );
#endif // LOG_STREAMINGTHREAD

    RAKI_ASSERT( _request );

    Request request( cancel, _request );

    m_requestCommandList.addElement( request );
}


void StreamingThread::closeReadRequest( StreamingRequest * const _request ) 
{
#ifdef LOG_STREAMINGTHREAD
    RAKI_OUTPUT("0x%x StreamingThread::closeReadRequest _stream 0x%x ", this, _request );
#endif // LOG_STREAMINGTHREAD

    RAKI_ASSERT( _request );
    RAKI_ASSERT( _request->getState() != StreamingRequest::readRequested );

    Request request( close, _request );

    m_requestCommandList.addElement( request );
}


bool StreamingThread::canProcessRequest()
{
    return true;
}





} // namespace raki 

