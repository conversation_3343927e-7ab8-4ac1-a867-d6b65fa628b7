#ifndef RAKI_STREAMINGREQUEST_H
#define RAKI_STREAMINGREQUEST_H


namespace raki
{




class SeekableStream;





class StreamingRequest
{

    friend class StreamingThread;

public:

    enum State
    {
        notUsed,
        readRequested,
        readFinished,
        readCancelled
    };

    StreamingRequest();
    virtual ~StreamingRequest();

    inline const State getState() const;
    inline const u64 getSizeRequested() const;
    inline const u64 getSizeRead() const;
    inline const u64 getPosition() const;
    inline void * getBuffer() const;

protected:

    void reinit();
    void setReadRequest( SeekableStream * _stream, const u64 _position, const u64 _sizeRequested, void * _buffer );
    inline void setState( const State _state );
    inline void setSizeRead( const u64 _sizeRead );

private:

    SeekableStream * m_stream;
    u64 m_position;
    u64 m_sizeRequested;
    u64 m_sizeRead;
    void * m_buffer;
    State m_state;

};



// inline implementation 

const StreamingRequest::State StreamingRequest::getState() const
{
    return m_state;
}

const u64 StreamingRequest::getSizeRequested() const
{
    return m_sizeRequested;
}

const u64 StreamingRequest::getSizeRead() const
{
    return m_sizeRead;
}

const u64 StreamingRequest::getPosition() const
{
    return m_position;
}

void * StreamingRequest::getBuffer() const
{
    return m_buffer;
}

void StreamingRequest::setState( const State _state )
{
    m_state = _state;
}

void StreamingRequest::setSizeRead( const u64 _sizeRead )
{
    m_sizeRead = _sizeRead;
}


} // namespace raki


#endif // RAKI_STREAMINGREQUEST_H