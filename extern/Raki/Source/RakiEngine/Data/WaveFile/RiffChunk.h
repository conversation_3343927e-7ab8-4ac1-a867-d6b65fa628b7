#ifndef RAKI_RIFFCHUNK_H
#define RAKI_RIFFCHUNK_H


#include <list>

#include "RakiEngine/System/RakiSTLAllocator.h"

#include "RakiEngine/Data/Serialization/SerializedObject.h"


namespace raki
{


class RiffChunk : public SerializedObject
{
public:

    RiffChunk();
    virtual ~RiffChunk();

    // from SerializedObject
    virtual void serialize( Serializer & _serializer );

    RiffChunk * getChunk( const char * _typeName );
    RiffChunk * getRiffOrList( const char * _typeName );

    const bool isList();
    const bool isRiff();

    void setEmptyRiff( u32 _typeId, u32 _riffOrListTypeId );
    void addChunk( const char * _typeName, const u32 _size, const void * _data );

    const bool changeToBigEndian( bool & dataIsPCMAndNeedsToBeSwapped );

    void recalculateRiffAndListSizes();

    inline const u32 getOffsetInStream();
    inline const u32 getTypeId();
    inline const u32 getRiffOrListTypeId();
    inline void * getData();
    inline const u32 getSize();
    inline const u32 getNbChildren();
    inline RiffChunk * getChild( u32 _childIndex );

private:

    void endOfSerializeList( Serializer & _serializer );
    void endOfserializeNormalChunk( Serializer & _serializer );

    typedef std::list< RiffChunk*, StlAllocator<RiffChunk*, Memory::engine> > RiffChunkList;
    RiffChunkList m_children;

    u32 m_offsetInStream;
    u32 m_typeId;
    u32 m_riffOrListTypeId;
    void * m_data;
    u32 m_size;
};




// inline implementation 

const u32 RiffChunk::getTypeId()
{
    return m_typeId;
}


const u32 RiffChunk::getRiffOrListTypeId()
{
    RAKI_ASSERT( isList() || isRiff() );

    return m_riffOrListTypeId;
}


void * RiffChunk::getData()
{
    return m_data;
}


const u32 RiffChunk::getSize()
{
    return m_size;
}


const u32 RiffChunk::getNbChildren()
{
    return (u32) m_children.size();
}


RiffChunk * RiffChunk::getChild( u32 _childIndex )
{
    RAKI_ASSERT( _childIndex < m_children.size() );

    RiffChunkList::iterator it = m_children.begin();

    u32 index = 0;

    while ( it != m_children.end() )
    {
        if ( index == _childIndex )
            return *it;

        ++index;
        ++it;
    }

    return NULL;
}


const u32 RiffChunk::getOffsetInStream()
{
    return m_offsetInStream;
}



} // namespace raki










#endif // RAKI_RIFFCHUNK_H

