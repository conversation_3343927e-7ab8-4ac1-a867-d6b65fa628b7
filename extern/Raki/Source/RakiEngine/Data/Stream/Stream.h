#ifndef RAKI_STREAM_H
#define RAKI_STREAM_H



namespace raki
{


class Stream
{
public:

    Stream( bool _streamIsWriting = true );
    virtual ~Stream();

    virtual const u64 stream( void * _data, const u64 _size ) = 0;

    inline const bool isReading() const;
    inline const bool isWriting() const;

    inline void setIsReading();
    inline void setIsWriting();

private:

    bool m_streamIsWriting;
};


const bool Stream::isReading() const
{
    return !m_streamIsWriting;
}

const bool Stream::isWriting() const
{
    return m_streamIsWriting;
}

void Stream::setIsReading()
{
    m_streamIsWriting = false;
}

void Stream::setIsWriting()
{
    m_streamIsWriting = true;
}



} // namespace raki










#endif // RAKI_STREAM_H

