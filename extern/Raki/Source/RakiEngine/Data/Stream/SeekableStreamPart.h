#ifndef RAKI_SEEKABLESTREAMPART_H
#define RAKI_SEEKABLESTREAMPART_H



//#include "RakiEngine/Data/Stream/SeekableStreamPart.h"


#include "RakiEngine/Data/Stream/SeekableStream.h"



namespace raki
{


    // this object is to be able to select an offset and a size in a SeekableStream and give 
    // another SeekableStream interface to this part of the original SeekableStream 

    class SeekableStreamPart 
        : public SeekableStream
    {
    public:

        SeekableStreamPart( SeekableStream * _stream, u64 _begin, u64 _size );

        SeekableStreamPart();

        virtual ~SeekableStreamPart();

        void set( SeekableStream * _stream, u64 _begin, u64 _size );

        virtual const u64 seek( const u64 _size, const SeekType _seekType );

        virtual const u64 getCurrentPos() const;

        virtual const u64 getSize() const;

        inline const u64 stream( void * _data, const u64 _size );

    private:

        SeekableStream * m_stream;
        u64 m_begin, m_size;


    };




} // namespace raki










#endif // RAKI_SEEKABLESTREAMPART_H

