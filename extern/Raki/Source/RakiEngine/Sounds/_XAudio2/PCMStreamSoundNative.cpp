#include "Precompiled.h"





#include "RakiEngine/Sounds/_XAudio2/PCMStreamSoundNative.h"





#include "RakiEngine/Initializer/EngineInitializer.h"
#include "RakiEngine/Sounds/Format.h"
#include "RakiEngine/System/Atomic/Atomic.h"
#include "RakiEngine/System/RakiTime.h"




//#define LOG_PCMSTREAMSOUNDNATIVE




namespace raki
{



PCMStreamSoundNative::PCMStreamSoundNative()
    : m_dataProvider( NULL )
    , m_isStopping( false )
    , m_waitingForSourceChangeDisablePull( false )
{

}


PCMStreamSoundNative::~PCMStreamSoundNative()
{

}


bool PCMStreamSoundNative::createVoice( const Format * _format )
{
    RAKI_ASSERT( _format );
    WAVEFORMATEX * waveFormatEx = (WAVEFORMATEX*)_format->getWaveFormatData();
    RAKI_ASSERT( ( waveFormatEx->wFormatTag == WAVE_FORMAT_PCM ) || ( waveFormatEx->wFormatTag == WAVE_FORMAT_ADPCM ) );
    RAKI_ASSERT( !m_sourceVoice );

    m_nbChannels = waveFormatEx->nChannels;

    m_nextBufferToSubmit = 0;
    m_sourceIndex = 0;

    // create voice 

    IXAudio2 * xaudio2 = EngineInitializer::singleton().getXAudio2();

    UINT32 flags = XAUDIO2_VOICE_NOSRC | XAUDIO2_VOICE_NOPITCH;
    if ( _format->isMusic() )
        flags |= XAUDIO2_VOICE_MUSIC;

    HRESULT hr = xaudio2->CreateSourceVoice( &m_sourceVoice, waveFormatEx, flags, 1.f, this ); 

    if ( hr !=  S_OK )
    {
        RAKI_ASSERT( !m_sourceVoice );
        return false;
    }

#ifdef LOG_PCMSTREAMSOUNDNATIVE
    if ( m_isStopping )
    {
        RAKI_OUTPUT( "0x%x PCMStreamSoundNative::createVoice >> m_isStopping = false", this );
    }
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = false;

    return true;
}


void PCMStreamSoundNative::destroyVoice()
{
    if ( m_sourceVoice )
    {
        m_sourceVoice->DestroyVoice();

        m_sourceVoice = NULL;
    }
}


void PCMStreamSoundNative::update()
{
    //RAKI_OUTPUT("0x%x PCMStreamSoundNative::update 0x%llx\n", this, getSamplePosition() );

    if ( !m_dataProvider )
        return;

    const u64 samplePosition = getSamplePosition();

    if ( shouldSubmitNextBuffer() )
    {
        u64 sourceSamplePositionBeforeSubmit = m_dataProvider->getCurrentSamplePosition();

        if ( !m_waitingForSourceChangeDisablePull && pullDataToBuffer( m_nextBufferToSubmit ) )
        {
            bool endOfStream = m_dataProvider->getRemainingByteSize() ? false : true;

            if ( submitBuffer( m_nextBufferToSubmit, endOfStream, sourceSamplePositionBeforeSubmit ) )
                m_nextBufferToSubmit = ( m_nextBufferToSubmit + 1 ) % STREAMSOUNDNATIVE_NBBUFFERS;
        }
        else if ( !m_dataProvider->getRemainingByteSize() )
        {
            m_waitingForSourceChangeDisablePull = true;

            if ( m_dataProvider->udpateAndChangeSourceWhenPossible( samplePosition ) )
            {
                m_waitingForSourceChangeDisablePull = false;

                ++m_sourceIndex;

                sourceSamplePositionBeforeSubmit = m_dataProvider->getCurrentSamplePosition();

                if ( pullDataToBuffer( m_nextBufferToSubmit ) )
                {
                    bool endOfStream = m_dataProvider->getRemainingByteSize() ? false : true;

                    if ( submitBuffer( m_nextBufferToSubmit, endOfStream, sourceSamplePositionBeforeSubmit ) )
                        m_nextBufferToSubmit = ( m_nextBufferToSubmit + 1 ) % STREAMSOUNDNATIVE_NBBUFFERS;
                }
                else
                {
                    setEmptySecondRead();
                }
            }

        }
    }

    if ( samplePosition < m_updateLastSamplePosition )
    {
        // we've passed end of a part > back to beginning of another part 
        m_dataProvider->resetState();
    }

    m_updateLastSamplePosition = samplePosition;
}


void PCMStreamSoundNative::stop()
{
    StreamSoundNative::stop();

#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT( "0x%x PCMStreamSoundNative::stop >> m_isStopping = %s", this, m_isStopping ? "TRUE" : "false" );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = false;
}


void PCMStreamSoundNative::prepare( SoundStreamDataProvider * _dataProvider )
{
    m_waitingForSourceChangeDisablePull = false;

    RAKI_ASSERT( _dataProvider );

    m_dataProvider = _dataProvider;
    m_dataProvider->resetState();

    u64 sourceSamplePositionBeforeSubmit = m_dataProvider->getCurrentSamplePosition();

    if ( pullDataToBuffer( m_nextBufferToSubmit ) )
    {
        bool endOfStream = m_dataProvider->getRemainingByteSize() ? false : true;

        if ( submitBuffer( m_nextBufferToSubmit, endOfStream, sourceSamplePositionBeforeSubmit ) )
            m_nextBufferToSubmit = ( m_nextBufferToSubmit + 1 ) % STREAMSOUNDNATIVE_NBBUFFERS;
    }
}


bool PCMStreamSoundNative::shouldSubmitNextBuffer()
{
    if ( !m_sourceVoice )
        return false;

    if ( m_startWaitingForFirstData )
        return true;

    return ( getNbSubmittedBuffers() < STREAMSOUNDNATIVE_NBBUFFERS );
}


bool PCMStreamSoundNative::submitBuffer( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit )
{
    RAKI_ASSERT( m_sourceVoice );
    RAKI_ASSERT( ( _bufferIndex >= 0 ) && ( _bufferIndex < STREAMSOUNDNATIVE_NBBUFFERS ) );

    Buffer & buffer = m_bufferArray[ _bufferIndex ];

#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT("PCMStreamSoundNative::submitBuffer _buffer %d size 0x%llx before submit 0x%llx _endOfStream %s \n", _bufferIndex, buffer.m_sizeToSubmit, _sourceSamplePositionBeforeSubmit, _endOfStream ? "TRUE" : "false" );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    RAKI_ASSERT( buffer.getBuffer() );
    RAKI_ASSERT( buffer.m_sizeToSubmit );

    XAUDIO2_BUFFER x2Buffer = { 0 };
    x2Buffer.pAudioData = (BYTE*)buffer.getBuffer();
    x2Buffer.AudioBytes = (UINT32)buffer.m_sizeToSubmit;

    RAKI_STATIC_ASSERT( STREAMBUFFERINFO_NB_INFOSTRUCT  >= STREAMSOUNDNATIVE_NBBUFFERS );
    StreamBufferInfo::Info * info = m_streamBufferInfo.getNextWriteInfo();

    info->setInfo( _bufferIndex, m_dataProvider->getClientData(), _sourceSamplePositionBeforeSubmit, m_sourceIndex, _endOfStream );

    x2Buffer.pContext = (void*)info;

    x2Buffer.Flags = _endOfStream ? XAUDIO2_END_OF_STREAM : 0;

#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT("PCMStreamSoundNative::submitBuffer %d size 0x%llx _endOfStream %s _sourceSamplePositionBeforeSubmit 0x%llx m_nbBuffersQueued %d\n", 
        _bufferIndex, buffer.m_sizeToSubmit, _endOfStream ? "TRUE" : "false", _sourceSamplePositionBeforeSubmit, 0/*m_nbBuffersQueued*/ );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    HRESULT hr = m_sourceVoice->SubmitSourceBuffer( &x2Buffer ); 

    if ( hr != S_OK )
    {
        RAKI_OUTPUT("StreamSoundNative::submitNextBuffer error in xaudio2 SubmitSourceBuffer\n");

        return false;
    }
    // mark buffer as empty 
    m_bufferArray[ _bufferIndex ].m_sizeToSubmit = 0;

#ifdef LOG_PCMSTREAMSOUNDNATIVE
    if ( m_isStopping )
    {
        RAKI_OUTPUT( "0x%x PCMStreamSoundNative::submitBuffer >> m_isStopping = false", this );
    }
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = false;

    m_startWaitingForFirstData = false;

    return true;
}


bool PCMStreamSoundNative::pullDataToBuffer( const int _bufferIndex )
{
    RAKI_ASSERT( m_dataProvider );
    RAKI_ASSERT( ( _bufferIndex >= 0 ) && ( _bufferIndex < STREAMSOUNDNATIVE_NBBUFFERS ) );

#ifdef RAKI_TARGET_DEBUG
    f32 begin = Time::getTime();
#endif // RAKI_TARGET_DEBUG

    Buffer & buffer = m_bufferArray[ _bufferIndex ];

    // check how much data is remaining : if there is less than max buffer size read entire remaining data
    // if not read only half of max buf size 

    u64 sizeRemaining = m_dataProvider->getRemainingByteSize();

    if ( !sizeRemaining )
        return false;

    if ( sizeRemaining <= m_doubleBufferMaxSize )
    {
        // read entire remaining data

        buffer.m_sizeToSubmit = m_dataProvider->pullData( buffer.getBuffer(), sizeRemaining );
    }
    else
    {
        // read only half of max buf size 

        buffer.m_sizeToSubmit = m_dataProvider->pullData( buffer.getBuffer(), m_doubleBufferMaxSize / 2 );
        buffer.m_playLength = 0;
    }

#ifdef RAKI_TARGET_DEBUG
    f32 duration = Time::getTime() - begin;

    if ( duration > 0.003f )
    {
        RAKI_OUTPUT("PCMStreamSoundNative::pullDataToBuffer took %.3f ms\n", 1000.f * duration );
    }
#endif // RAKI_TARGET_DEBUG

    return buffer.m_sizeToSubmit != 0;
}


void PCMStreamSoundNative::setEmptySecondRead()
{
#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT( "0x%x PCMStreamSoundNative::setEmptySecondRead >> m_isStopping = true", this );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = true;
}


bool PCMStreamSoundNative::isStopping()
{
    return m_isStopping;
}


bool PCMStreamSoundNative::isStopped()
{
    return !isPlaying();
}


void PCMStreamSoundNative::requestEndAfterSubmittedBuffers()
{
#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT( "0x%x PCMStreamSoundNative::requestEndAfterSubmittedBuffers >> m_isStopping = true", this );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = true;
}


void PCMStreamSoundNative::resetRequestEndAfterSubmittedBuffers()
{
#ifdef LOG_PCMSTREAMSOUNDNATIVE
    RAKI_OUTPUT( "0x%x PCMStreamSoundNative::resetRequestEndAfterSubmittedBuffers >> m_isStopping = false", this );
#endif // LOG_PCMSTREAMSOUNDNATIVE

    m_isStopping = false;
}







} // namespace raki

