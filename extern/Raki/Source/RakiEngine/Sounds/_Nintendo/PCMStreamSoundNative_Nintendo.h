#ifndef RAKI_PCMSTREAMSOUNDNATIVE_NINTENDO_H
#define RAKI_PCMSTREAMSOUNDNATIVE_NINTENDO_H

#pragma once

#include "RakiEngine/System/RakiTypes.h"
#include "RakiEngine/Sounds/_Nintendo/StreamSoundNative_Nintendo.h"

namespace raki
{
    class Format;

    class PCMStreamSoundNative
        : public StreamSoundNative
    {
    public:
        PCMStreamSoundNative() = default;
        virtual ~PCMStreamSoundNative() override = default;
        virtual bool createVoice(const Format * _format) override;
        virtual void destroyVoice() override;
        virtual void prepare(SoundStreamDataProvider * _dataProvider);
        virtual void update();
        virtual bool isStopping() override;
        virtual bool isStopped();

        virtual void requestEndAfterSubmittedBuffers();
        virtual void resetRequestEndAfterSubmittedBuffers();
    };

} // namespace raki

#endif // RAKI_PCMSTREAMSOUNDNATIVE_NINTENDO_H
