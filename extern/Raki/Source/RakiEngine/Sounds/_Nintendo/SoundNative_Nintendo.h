#ifndef RAKI_SOUNDNATIVE_NINTENDO_H
#define RAKI_SOUNDNATIVE_NINTENDO_H

#pragma once

#include "RakiEngine/System/RakiTypes.h"
#include "RakiEngine/Sounds/VoiceBase.h"

namespace raki
{
    class Format;

    class SoundNative
        : public VoiceBase
    {
    public:

        SoundNative();
        virtual ~SoundNative();
        virtual bool createVoice( const Format * _format ) = 0;
        virtual void destroyVoice() = 0;
        virtual void start();
        virtual void stop();
        virtual void pause();
        virtual void resume();
        virtual void internalSetDecibelVolume( f32 _decibelVolume );
        virtual void setPitch( const f32 _pitch );
        virtual void setPan( const f32 _pan );

        inline bool hasValidLowLevelVoice() const { return m_multistreamChannel >= 0; }

    protected:
        void internalSetLinearVolAndPanOnMono( const i32 _multipliedLinearVolume, const i32 _multipliedPan );
        int m_multistreamChannel;
        int m_nbChannels;
        u32 m_samplingRate;
        i32 m_multipliedLinearVolume;
        i32 m_multipliedPan;
    };

} // namespace raki

#endif // RAKI_SOUNDNATIVE_NINTENDO_H


