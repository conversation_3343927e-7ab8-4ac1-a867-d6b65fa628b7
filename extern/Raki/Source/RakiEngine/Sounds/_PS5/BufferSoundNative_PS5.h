#ifndef RAKI_BUFFERSOUNDNATIVE_PS5_H
#define RAKI_BUFFERSOUNDNATIVE_PS5_H

#pragma once

#include "RakiEngine/System/RakiTypes.h"
#include "RakiEngine/Sounds/_PS5/SoundNative_PS5.h"

namespace raki
{
    class Format;
    class MemoryBlock;

    class BufferSoundNative
        : public SoundNative
    {
    public:
        BufferSoundNative();
        virtual ~BufferSoundNative();
        virtual bool createVoice( const Format * _format );
        virtual void destroyVoice();
        virtual bool isPlaying() const;
    };

} // namespace raki

#endif // RAKI_BUFFERSOUNDNATIVE_PS5_H

