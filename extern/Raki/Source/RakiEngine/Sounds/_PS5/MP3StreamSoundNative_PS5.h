#ifndef RAKI_MP3STREAMSOUNDNATIVE_PS5_H
#define RAKI_MP3STREAMSOUNDNATIVE_PS5_H

#pragma once

#include "RakiEngine/Sounds/_PS5/StreamSoundNative_PS5.h"

namespace raki
{
    class Format;

    class MP3StreamSoundNative
        : public StreamSoundNative
    {
    public:
        MP3StreamSoundNative();
        virtual ~MP3StreamSoundNative();
        virtual bool createVoice( const Format * _format );
        virtual void destroyVoice();
        virtual void prepare( SoundStreamDataProvider * _dataProvider );
        virtual void update();
        virtual bool isStopping(); 
        virtual bool isStopped(); 
        virtual void callbackUpdate();
    };

} // namespace raki

#endif // RAKI_MP3STREAMSOUNDNATIVE_PS5_H
