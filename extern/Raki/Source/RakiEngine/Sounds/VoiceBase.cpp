#include "Precompiled.h"



#include "RakiEngine/Sounds/VoiceBase.h"







namespace raki
{


VoiceBase::VoiceBase()
    : m_isPendingSyncStart( false )
    , m_pendingSyncStartClientData( NULL )
{

}

VoiceBase::~VoiceBase()
{

}


void VoiceBase::startFromAudioCallback()
{
#ifdef RAKI_PLATFORM_WII
    RAKI_ASSERT( 0 ); // we should use audio callback start instead 
#endif // RAKI_PLATFORM_WII
    start();
}


} // namespace raki

