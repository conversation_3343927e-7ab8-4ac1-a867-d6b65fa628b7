#ifndef RAKI_SYNCCONTEXTMANAGER_H
#define RAKI_SYNCCONTEXTMANAGER_H



#include "RakiEngine/System/Containers/CircularCommandList.h"


namespace raki
{


class SyncContextClient;


class SyncContextManager
{

protected:

    SyncContextManager( u32 _maxNbClients );
    virtual ~SyncContextManager();

    void RequestRegisterClient( SyncContextClient * _client );
    void RequestUnregisterClient( SyncContextClient * _client );

    virtual void Synchronize(); 

private:

    void ProcessRegisterRequests();
    void ProcessUnregisterRequests();

    void AddClient( SyncContextClient * _client );
    void RemoveClient( SyncContextClient * _client );

    SyncContextClient ** m_clientArray;
    u32 m_nbClients, m_maxNbClients;

    CircularCommandList<SyncContextClient*> m_registrationList;
    CircularCommandList<SyncContextClient*> m_unregistrationList;

};



} // namespace raki




#endif // RAKI_SYNCCONTEXTMANAGER_H


