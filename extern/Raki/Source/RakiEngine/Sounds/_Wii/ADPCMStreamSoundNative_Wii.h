#ifndef RAKI_ADPCMSTREAMSOUNDNATIVE_WII_H
#define RAKI_ADPCMSTREAMSOUNDNATIVE_WII_H





#include "RakiEngine/Sounds/_WII/StreamSoundNative_WII.h"





#define ADPCMSTREAMSOUNDNATIVE_SETBUFFERADDRESSES           3001
#define ADPCMSTREAMSOUNDNATIVE_SETLOOPADDRESSONBUFFER       3002
#define ADPCMSTREAMSOUNDNATIVE_SETVOICEADPCM                3003
#define ADPCMSTREAMSOUNDNATIVE_SETEMPTYSECONDREAD           3004



namespace raki
{


class Format;


class ADPCMStreamSoundNative
    : public StreamSoundNative
{
public:

    ADPCMStreamSoundNative( u32 _nbChannels );

    virtual ~ADPCMStreamSoundNative();

    virtual bool createVoice( const Format * _format );

    virtual void prepare( WiiAdpcmSoundStreamDataProvider * _dataProvider );

    virtual void update();

    virtual bool isStopping(); 

    virtual bool isStopped(); 

    virtual void stop();

    // from AudioCallbackCommandObject
    virtual void processCommand( const u32 _commandId, void * _paramArray[] );
    
private:

    // AudioCallbackManager::PostCallback
    virtual void postCallback();
    
    void setBufferAddresses( const int _bufferIndex, const u32 _sampleSize );
    
    void audiocallback_setBufferAddresses( const int _bufferIndex, const u32 _sampleSize );

    void audiocallback_setEndAddressOnBuffer( const int _bufferIndex, const u32 _sampleSize, const bool _isLastBuffer );
    
    void setLoopAddressOnBuffer( const int _bufferIndex );
    
    void audiocallback_setLoopAddressOnBuffer( const int _bufferIndex );
    
    void copyInterlacedBufferToChannelBuffers( const int _bufferIndex );

    bool createWithData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit );

    bool audiocallback_createWithData( const int _bufferIndex, const bool _endOfStream, const u32 _sourceSamplePositionBeforeSubmit );

    virtual void voiceIsReacquiredByAX();
    
    void needsMoreData( const int _bufferIndex );

    bool shouldSubmitNextBuffer();

    bool submitData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit, const bool _writeAddresses );

    void setEmptySecondRead(); 

    void audiocallback_setEmptySecondRead(); 

    bool pullDataToBuffer( const int _bufferIndex );
    
    void setVoiceAdpcm();

    void audiocallback_setVoiceAdpcm();

    inline const u32 getSampleNibbleAdress( const u32 _sample );

    inline const u32 getBufferNibbleAdress( void * _buffer  ); 

    WiiAdpcmSoundStreamDataProvider * m_dataProvider;

    u64 m_sourceIndex;
    
    u32 m_previousBufferSampleSize;

    // debugging
    u32 m_beginAddressArrayL[2], m_endAddressArrayL[2], m_loopAddressArrayL[2];
    u32 m_beginAddressArrayR[2], m_endAddressArrayR[2], m_loopAddressArrayR[2];

    int m_countBuffersForSetVoiceTypeStream;

    AXPBADPCMLOOP  m_AdpcmLoopContextL;
    AXPBADPCMLOOP  m_AdpcmLoopContextR;
    
    bool m_isStopping;

    bool m_needsMoreDataArray[ 2 ];
    

};




// inline implementation 


const u32 ADPCMStreamSoundNative::getSampleNibbleAdress( const u32 _sample )
{
    return ( ( _sample / 14 ) * 16 ) + ( _sample % 14 ) + 2;
}

const u32 ADPCMStreamSoundNative::getBufferNibbleAdress( void * _buffer  )
{
    return ( (u32)OSCachedToPhysical( _buffer ) ) << 1;
}



} // namespace raki




#endif // RAKI_ADPCMSTREAMSOUNDNATIVE_WII_H


