#ifndef RAKI_SOUNDS_SINENOTEMEMBLOCK_H
#define RAKI_SOUNDS_SINENOTEMEMBLOCK_H




#include "RakiEngine/Data/MemoryBlock/MemoryBlock.h"






namespace raki
{


class SineNoteMemBlock 
    : public MemoryBlock
{
public:
    SineNoteMemBlock ();

    virtual ~SineNoteMemBlock ();

    void makeBuffer( const u8 _midiNote, const u32 _samplingRate, const f32 _duration, 
        const f32 _volumeDecibelsAtBeginning, const f32 _volumeDecibelsAfterOneSecond );

    inline WAVEFORMATEX * getFormatData();

    inline u32 getFormatSize();

private:

    WAVEFORMATEX m_waveFormat;

};


WAVEFORMATEX * SineNoteMemBlock::getFormatData()
{
    return &m_waveFormat;
}


u32 SineNoteMemBlock::getFormatSize()
{
    return sizeof( m_waveFormat );
}


} // namespace raki




#endif // RAKI_SOUNDS_SINENOTEMEMBLOCK_H


