#ifndef RAKI_VOICEBASE_H
#define RAKI_VOICEBASE_H





#include "RakiEngine/Routing/RoutingChild.h"




namespace raki
{

class Format;



class VoiceBase
    : public RoutingChild
{
public:

    VoiceBase();

    virtual ~VoiceBase();

    virtual void destroyVoice() = 0;

    virtual void start() = 0;

    virtual void startFromAudioCallback();

    virtual void stop() = 0;

    virtual void pause() = 0;

    virtual void resume() = 0;

    virtual bool isPlaying() const = 0;

    virtual void setPitch( f32 _pitch ) = 0;

    virtual void setPan( f32 _pan ) = 0; // pan [ -1.f - 1.f ]

    inline void setPendingSyncStart( void * _clientData );

    inline void resetPendingSyncStart();

    inline bool getIsPendingSyncStart() const;

    inline void * getPendingSyncStartClientData();

private:

    volatile int m_isPendingSyncStart;

    void* m_pendingSyncStartClientData;
};



// inline implementation 

void VoiceBase::setPendingSyncStart( void * _clientData )
{
    m_isPendingSyncStart = 1;
    m_pendingSyncStartClientData = _clientData;
}

void VoiceBase::resetPendingSyncStart()
{
    m_isPendingSyncStart = 0;
    m_pendingSyncStartClientData = NULL;
}

bool VoiceBase::getIsPendingSyncStart() const
{
    return m_isPendingSyncStart ? true : false;
}

void * VoiceBase::getPendingSyncStartClientData()
{
    return m_pendingSyncStartClientData;
}


} // namespace raki




#endif // RAKI_VOICEBASE_H


