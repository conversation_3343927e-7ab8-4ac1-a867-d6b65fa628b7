#ifndef RAKI_PCMSTREAMSOUNDNATIVE_PS3_H
#define RAKI_PCMSTREAMSOUNDNATIVE_PS3_H





#include "RakiEngine/Sounds/_PS3/StreamSoundNative_PS3.h"




namespace raki
{


class Format;


class PCMStreamSoundNative
    : public StreamSoundNative
{
public:

    PCMStreamSoundNative();

    virtual ~PCMStreamSoundNative();

    virtual bool createVoice( const Format * _format );

    virtual void destroyVoice();

    virtual void prepare( SoundStreamDataProvider * _dataProvider );

    virtual void update();

    virtual bool isStopping(); 

    virtual bool isStopped(); 

private:

    bool createWithData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit );

    static void StreamCallback(int streamNumber, void * userData, int callbackType, void * pWriteBuffer, int nBufferSize);

    void needsMoreData();

    bool shouldSubmitNextBuffer();

    bool submitMoreData( const int _bufferIndex, const bool _endOfStream, const u64 _sourceSamplePositionBeforeSubmit );

    void setEmptySecondRead(); 

    bool pullDataToBuffer( const int _bufferIndex );

    SoundStreamDataProvider * m_dataProvider;

    u64 m_sourceIndex;

    volatile bool m_needsMoreData;

    bool m_isStopping;
};





} // namespace raki




#endif // RAKI_PCMSTREAMSOUNDNATIVE_PS3_H


