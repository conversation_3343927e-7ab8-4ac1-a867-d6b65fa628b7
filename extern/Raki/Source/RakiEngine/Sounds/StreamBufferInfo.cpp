#include "Precompiled.h"



#include "RakiEngine/Sounds/StreamBufferInfo.h"



#include "RakiEngine/Sounds/SoundStreamDataBuffer.h"




//#define LOG_STREAMBUFFERINFO





namespace raki
{


    // StreamBufferInfo implementation 

    StreamBufferInfo::StreamBufferInfo()
        : m_nextReadInfoIndex( 0 )
        , m_nextWriteInfoIndex( 0 )
        , m_measuredSamplesAtBeginningOfBuffer( 0 )
        , m_activeBufferInfo( NULL )
        , m_streamUpdateClient( 0 )
    {
        for ( int i = 0 ; i < STREAMBUFFERINFO_NB_INFOSTRUCT ; ++i )
        {
            m_infoArray[ i ].setStreamBufferInfo( this );
        }
    }


    void StreamBufferInfo::reinit()
    {
        m_nextReadInfoIndex = 0;
        m_nextWriteInfoIndex = 0;
        m_measuredSamplesAtBeginningOfBuffer = 0;
        m_activeBufferInfo = NULL;
    }


    StreamBufferInfo::Info * StreamBufferInfo::getNextReadInfo()
    {
        RAKI_ASSERT( m_nextReadInfoIndex < STREAMBUFFERINFO_NB_INFOSTRUCT );

        Info * nextReadInfo = &m_infoArray[ m_nextReadInfoIndex ];

#ifdef LOG_STREAMBUFFERINFO
#if defined ( RAKI_USING_XAUDIO2 )
        RAKI_OUTPUT("0x%x StreamBufferInfo::getNextReadInfo _dataBuffer 0x%x _clientData 0x%x _sourceSamplePositionBeforeSubmit 0x%llx \n", 
            this, nextReadInfo->getDataBuffer(), nextReadInfo->getClientData(), nextReadInfo->getSourceSamplePositionBeforeSubmit() );
#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )
        RAKI_OUTPUT("0x%x StreamBufferInfo::getNextReadInfo _dataBuffer 0x%x _clientData 0x%x _sourceSamplePositionBeforeSubmit 0x%llx sampleSize 0x%x\n", 
            this, nextReadInfo->getDataBuffer(), nextReadInfo->getClientData(), nextReadInfo->getSourceSamplePositionBeforeSubmit(),
            nextReadInfo->getBufferSampleSize() );
#endif 
#endif // LOG_STREAMBUFFERINFO

        m_nextReadInfoIndex = ( m_nextReadInfoIndex + 1 ) % STREAMBUFFERINFO_NB_INFOSTRUCT;

        return nextReadInfo;
    }


    StreamBufferInfo::Info * StreamBufferInfo::getNextWriteInfo()
    {
        RAKI_ASSERT( m_nextWriteInfoIndex < STREAMBUFFERINFO_NB_INFOSTRUCT );

        Info * nextWriteInfo = &m_infoArray[ m_nextWriteInfoIndex ];

// #ifdef LOG_STREAMBUFFERINFO
//         RAKI_OUTPUT("0x%x StreamBufferInfo::getNextWriteInfo _dataBuffer 0x%x _clientData 0x%x _sourceSamplePositionBeforeSubmit 0x%llx \n", 
//             this, nextWriteInfo->getDataBuffer(), nextWriteInfo->getClientData(), nextWriteInfo->getSourceSamplePositionBeforeSubmit() );
// #endif // LOG_STREAMBUFFERINFO

        m_nextWriteInfoIndex = ( m_nextWriteInfoIndex + 1 ) % STREAMBUFFERINFO_NB_INFOSTRUCT;

        return nextWriteInfo;
    }




    // StreamBufferInfo::Info implementation 

    StreamBufferInfo::Info::Info()
        : m_sourceIndex( 0 )
        , m_sourceSamplePositionBeforeSubmit( 0 )
#if defined ( RAKI_USING_XAUDIO2 )
        , m_bufferIndex( 0 )
#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )
        , m_dataBuffer( NULL )
        , m_bufferSampleSize( 0 )
#endif 
//        , m_nbBuffersQueued( 0 )
        , m_clientData( NULL )
        , m_streamBufferInfo( NULL )
        , m_isLastBuffer( false )
    {
    }


    void StreamBufferInfo::Info::update( u64 samplePosition )
    {
/*        if ( m_soundStreamDataProvider )
        {
            f64 doubleTicks = (f64) samplePosition / m_soundStreamDataProvider->getSamplesPerTicks();
            u32 ticks = (u32)doubleTicks;

            if ( ( ticks % ( RAKI_MUSICALTIME_NB_SUBDIVISIONS_IN_BEAT * 4 ) ) == 0 )
            {
                u32 measure = ticks / ( RAKI_MUSICALTIME_NB_SUBDIVISIONS_IN_BEAT * 4 );
                u32 beat = ticks - measure * ( RAKI_MUSICALTIME_NB_SUBDIVISIONS_IN_BEAT * 4 );
                beat /= RAKI_MUSICALTIME_NB_SUBDIVISIONS_IN_BEAT;

                RAKI_OUTPUT( "StreamBufferInfo::Info::update samplePosition %d ticks %d - measure %d beat %d\n", samplePosition, ticks, measure, beat );

                RAKI_OUTPUT( "0x%x - m_soundStreamDataProvider 0x%x - sizeInTicks %d sizeInBars %d nbBeatsPerBar %d samplesPerTicks %.3f entireSampleSize %d\n", 
                    this, m_soundStreamDataProvider, m_soundStreamDataProvider->getSizeInTicks(), m_soundStreamDataProvider->getSizeInBars(), m_soundStreamDataProvider->getNbBeatsPerBar(), 
                    m_soundStreamDataProvider->getSamplesPerTicks(), m_soundStreamDataProvider->getEntireSampleSize() );

            }

        }
*/
        m_streamBufferInfo->updateStreamPosition( m_clientData, samplePosition );
    }


#if defined ( RAKI_USING_XAUDIO2 )

    void StreamBufferInfo::Info::setInfo( const u32 _bufferIndex, const void * _clientData, 
        const u64 _sourceSamplePositionBeforeSubmit, const u64 _sourceIndex, const bool _isLastBuffer )
    {
#ifdef LOG_STREAMBUFFERINFO
        RAKI_OUTPUT("0x%x StreamBufferInfo::Info::setInfo _bufferIndex %d _clientData 0x%x _sourceSamplePositionBeforeSubmit 0x%llx _sourceIndex %d\n", 
            this, _bufferIndex, _clientData, _sourceSamplePositionBeforeSubmit, _sourceIndex );
#endif // LOG_STREAMBUFFERINFO

        m_bufferIndex = _bufferIndex;
        m_clientData = _clientData;
        m_sourceIndex = _sourceIndex;
        m_sourceSamplePositionBeforeSubmit = _sourceSamplePositionBeforeSubmit;
        m_isLastBuffer = _isLastBuffer;
    }

#elif defined ( RAKI_PLATFORM_PS3 ) || defined ( RAKI_PLATFORM_WII )

    void StreamBufferInfo::Info::setInfo( const void * _dataBuffer, const void * _clientData, 
        const u64 _sourceSamplePositionBeforeSubmit, const u32 _bufferSampleSize, const u64 _sourceIndex, const bool _isLastBuffer )
    {
#ifdef LOG_STREAMBUFFERINFO
        RAKI_OUTPUT("0x%x StreamBufferInfo::Info::setInfo _dataBuffer 0x%x _clientData 0x%x _sourceSamplePositionBeforeSubmit 0x%llx _sourceIndex %d _bufferSampleSize 0x%x\n", 
            this, _dataBuffer, _clientData, _sourceSamplePositionBeforeSubmit, _sourceIndex, _bufferSampleSize );
#endif // LOG_STREAMBUFFERINFO

        m_dataBuffer = _dataBuffer;
        m_clientData = _clientData;
        //m_nbBuffersQueued = 0;
        m_bufferSampleSize = _bufferSampleSize;
        m_sourceIndex = _sourceIndex;
        m_sourceSamplePositionBeforeSubmit = _sourceSamplePositionBeforeSubmit;
        m_isLastBuffer = _isLastBuffer;
    }

#endif 





    StreamBufferInfo::StreamUpdateClient::~StreamUpdateClient()
    {
    }



} // namespace raki 

