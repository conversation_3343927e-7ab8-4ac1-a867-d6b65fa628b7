#include "precompiled_gameplay_rayman.h"

#ifndef _ITF_RAY_CHEATMANAGER_H_
#include "rayman/gameplay/Managers/Ray_CheatManager.h"
#endif //_ITF_RAY_CHEATMANAGER_H_

#ifndef _ITF_RAY_GAMEMANAGER_H_
#include "rayman/gameplay/Ray_GameManager.h"
#endif //_ITF_RAY_GAMEMANAGER_H_

#ifndef _ITF_RAY_GAMEPLAYTYPES_H_
#include "rayman/gameplay/Ray_GameplayTypes.h"
#endif //_ITF_RAY_GAMEPLAYTYPES_H_

#ifndef _ITF_COMMANDARGS_H_
#include "engine/common/CommandArgs.h"
#endif // _ITF_COMMANDARGS_H_

namespace ITF
{
    Ray_CheatManager::Ray_CheatManager()
    : m_allPlayersTogether(bfalse)
    , m_unlockAllCostumes(bfalse)
    , m_teethSequence(bfalse)
    , m_timeAttackEnabled(bfalse)
    , m_allWorldMap(bfalse)
    , m_allSkullTeeth(bfalse)
    {
    }

    Ray_CheatManager::~Ray_CheatManager()
    {
    }

    void Ray_CheatManager::init( const CommandArgs& _args )
    {
        CheatManager::init(_args);

        String szArg;

        if (_args.find("cheatAllPlayersTogether", szArg))
        {
            m_allPlayersTogether = (szArg.atoi32() != 0) ? btrue : bfalse;
        }
    }

    void Ray_CheatManager::update(f32 _dt)
    {
        CheatManager::update(_dt);
#ifdef ITF_SUPPORT_CHEAT
// Special E3
#ifndef ITF_DEMO
        if (!getActive())
        {
            return;
        }
#endif

#endif //ITF_SUPPORT_CHEAT
    }

    void Ray_CheatManager::teleportToCheckpoint( GameManager::teleportTarget _target )
    {
#ifdef ITF_CONSOLE

//###E3HACK
#ifndef ITF_DEMO
        CheatManager::teleportToCheckpoint(_target);
#endif // !ITF_DEMO

#else
        switch(_target)
        {
        case GameManager::NextCheckPoint:
            RAY_GAMEMANAGER->triggerTeleportCheatSequence(_target);
            break;

        case GameManager::PrevCheckPoint:
            RAY_GAMEMANAGER->triggerTeleportCheatSequence(_target);
            break;

        case GameManager::FirstCheckPoint:
            RAY_GAMEMANAGER->triggerTeleportCheatSequence(_target);
            break;

        case GameManager::LastCheckPoint:
            RAY_GAMEMANAGER->triggerTeleportCheatSequence(_target);
            break;

        default:
            ITF_ASSERT(0);
            break;
        }
#endif
    }

    void Ray_CheatManager::forceAllPlayersTogether( bbool _val )
    {
        if ( m_allPlayersTogether != _val )
        {
            m_allPlayersTogether = _val;
        }
    }


    void Ray_CheatManager::onReceive( u32 deviceID /* player */, f32 axis, const StringID& action )
    {
#ifdef ITF_SUPPORT_CHEAT
        if(action==ITF_GET_STRINGID_CRC(CHEAT_JUSTBEDEAD, 651860516))
        {
            Player* player = GAMEMANAGER->getPlayer(deviceID);
            if(player)
                player->toggleDeadMode();
        }
        else if(action==ITF_GET_STRINGID_CRC(CHEAT_INVINCIBLE, 1288607354))
        {
            Ray_Player* player = static_cast<Ray_Player*>(RAY_GAMEMANAGER->getPlayer(deviceID));
            if(player)
                player->setInvincible(!player->getInvincible());
        }
        else if(action==ITF_GET_STRINGID_CRC(CHEAT_ALLTOGETHER, 2538788027))
        {
            forceAllPlayersTogether(!m_allPlayersTogether);
        }
#endif //ITF_SUPPORT_CHEAT
    }

#ifdef ITF_SUPPORT_CHEAT
    void Ray_CheatManager::unlockAllLevels()
    {
        RAY_GAMEMANAGER->setWMSpotState(s_JU_A, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MU_A, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_FO_A, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_OC_A, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_A, SPOT_STATE_COMPLETED);

        RAY_GAMEMANAGER->setWMSpotState(s_JU_B, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MU_B, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_FO_B, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_OC_B, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_B, SPOT_STATE_COMPLETED);

        RAY_GAMEMANAGER->setWMSpotState(s_ju_bossmaster, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_BossBird, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_OC_B3, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_FO_B3, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_B3, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_B4, SPOT_STATE_COMPLETED);

        RAY_GAMEMANAGER->setWMSpotState(s_OC_A1, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_SHOOTER_A, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_MO_SHOOTER_B, SPOT_STATE_COMPLETED);
        RAY_GAMEMANAGER->setWMSpotState(s_DE_A1, SPOT_STATE_COMPLETED);
    }
#endif //ITF_SUPPORT_CHEAT

}
