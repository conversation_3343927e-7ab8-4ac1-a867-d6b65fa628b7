#ifndef _ITF_FILEWATCHER_IPAD_H_
#define _ITF_FILEWATCHER_IPAD_H_

#ifndef _ITF_SYNCHRONIZE_IPAD_H_
#include "engine/system/Synchronize.h"
#endif //_ITF_SYNCHRONIZE_IPAD_H_

namespace ITF
{
    class WatchDirectoryListener
    {
    };

///----------------------------------------------------------------------------------------------------------------

   struct <PERSON><PERSON>ileEvent
   {
   };

///----------------------------------------------------------------------------------------------------------------

   /// Directory watcher for a specific folder
   class WatchDirectory
   {
   };

///----------------------------------------------------------------------------------------------------------------

   ///Manager to handle directory notification
   class FileWatcher
   {
   };
///----------------------------------------------------------------------------------------------------------------
}

#endif //_ITF_FILEWATCHER_IPAD_H_