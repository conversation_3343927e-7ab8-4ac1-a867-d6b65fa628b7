#ifndef _ITF_VIDEOHANDLE_H_
#define _ITF_VIDEOHANDLE_H_

namespace  ITF
{
    class  videoHandleBase
    {
    public:

        videoHandleBase():m_playFromMemory(bfalse),m_loop(bfalse),m_opened(bfalse),m_stopped(btrue),m_alpha(1.0f),m_fps(1.0f),m_totalTimeInSeconds(0.0f),m_isPaused(bfalse)
        {

        }

        const bbool isLoop() const {return m_loop;}
        const bbool isStopped() const {return m_stopped;}
        const bbool isOpened() const { return m_opened;}
        const bbool isPlayedFromMemory() const {return m_playFromMemory;}
        void setPlayedFromMemory(bbool _bPlayFromMemory) {m_playFromMemory = _bPlayFromMemory;}
        
        void stop() {m_stopped = btrue;}
        ITF_INLINE bbool isPaused() const { return m_isPaused; }

        void setAlpha(f32 _alpha)               {m_alpha = _alpha;}
        const f32 getAlpha()         const      {return m_alpha;}

        const f32 getTotalTime()     const      {return m_totalTimeInSeconds;}
 
    protected:

        bbool   m_opened;
        bbool   m_loop;
        bbool   m_stopped;
        bbool   m_isPaused;
        f32     m_alpha;
        bbool   m_playFromMemory;

        f32     m_fps;
        f32     m_totalTimeInSeconds;

    };
}


#ifdef ITF_X360

#ifndef _ITF_VIDEOHANDLE_X360_H_
#include "engine/video/videoHandle_X360.h"
#endif //_ITF_VIDEOHANDLE_X360_H_

#elif defined(ITF_WINDOWS)

#include "engine/video/videoHandle_WIN.h"

#elif defined(ITF_PS3)
#include "engine/video/videoHandle_PS3.h"

#elif defined(ITF_WII)
#include "engine/video/videoHandle_WII.h"

#else

//not supported

namespace ITF
{
    class videoHandle : public videoHandleBase
    {
    public:

        void destroy();
        void update() {};
        void play();
        void pause() {}
        void close() {};

        bbool open(const String& _filename) {return btrue;}
        void stop() {};
        void render() {};

        bbool getCurrentTime(f32& _timeInSeconds) {return bfalse;};
    };
}

#endif

#endif //_ITF_VIDEOHANDLE_H_
